"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx":
/*!**************************************************!*\
  !*** ./src/app/ui/crew-training/create/form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew-training/create/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TrainingForm = (param)=>{\n    let { trainingID = 0, memberId = 0, trainingTypeId = 0, vesselId = 0 } = param;\n    var _rawTraining_VesselID, _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _vessels_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Date());\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [vesselID, setVesselID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(vesselId);\n    const [fieldImages, setFieldImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // GraphQL queries to replace the problematic function calls\n    const [queryTrainingTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.CREW_TRAINING_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingTypes.nodes;\n            if (data) {\n                setTrainingTypes(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingTypes error\", error);\n        }\n    });\n    const [queryTrainingSessionByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.TRAINING_SESSION_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingSession;\n            if (data) {\n                handleSetTraining(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingSession error\", error);\n        }\n    });\n    const [queryTrainingTypeByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.TRAINING_TYPE_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingType;\n            if (data) {\n                setTraining((prevTraining)=>({\n                        ...prevTraining,\n                        TrainingTypes: [\n                            data.id\n                        ]\n                    }));\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingType error\", error);\n        }\n    });\n    const [getFieldImages] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_SECTION_MEMBER_IMAGES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCaptureImages.nodes;\n            if (data) {\n                setFieldImages(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getFieldImages error\", error);\n        }\n    });\n    // useEffect hooks to replace the problematic function calls\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training types on component mount\n        queryTrainingTypes();\n    }, []) // Empty dependency array - only run once on mount\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training session by ID when trainingID changes\n        if (trainingID > 0) {\n            queryTrainingSessionByID({\n                variables: {\n                    id: trainingID\n                }\n            });\n        }\n    }, [\n        trainingID\n    ]) // Only depend on trainingID\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training type by ID when trainingTypeId changes\n        if (trainingTypeId > 0) {\n            queryTrainingTypeByID({\n                variables: {\n                    id: trainingTypeId\n                }\n            });\n        }\n    }, [\n        trainingTypeId\n    ]) // Only depend on trainingTypeId\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (trainingID > 0) {\n            getFieldImages({\n                variables: {\n                    filter: {\n                        trainingSessionID: {\n                            eq: trainingID\n                        }\n                    }\n                }\n            });\n        }\n    }, [\n        trainingID\n    ]) // Only depend on trainingID\n    ;\n    const refreshImages = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async ()=>{\n        await getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    }, [\n        getFieldImages,\n        trainingID\n    ]);\n    const handleSetTraining = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((training)=>{\n        var _training_procedureFields;\n        const tDate = new Date(training.date);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.date).format(\"YYYY-MM-DD\"),\n            Members: training.members.nodes.map((m)=>m.id),\n            TrainerID: training.trainer.id,\n            TrainingSummary: training.trainingSummary,\n            TrainingTypes: training.trainingTypes.nodes.map((t)=>t.id),\n            // VesselID: training.vessel.id,\n            VesselID: training.vesselID\n        };\n        const members = training.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        const vesselCrewIds = training.vessel.seaLogsMembers.nodes.map((slm)=>+slm.id);\n        const vesselCrews = members.filter((m)=>vesselCrewIds.includes(+m.value));\n        const signatures = training.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        // Batch all state updates using React's automatic batching\n        setTrainingDate(tDate);\n        setRawTraining(training);\n        setTraining(trainingData);\n        setContent(training.trainingSummary);\n        setSelectedMemberList(vesselCrews);\n        setSignatureMembers(signatures);\n        // Initialize buffer with existing procedure field data for updates\n        if ((_training_procedureFields = training.procedureFields) === null || _training_procedureFields === void 0 ? void 0 : _training_procedureFields.nodes) {\n            const existingProcedureChecks = training.procedureFields.nodes.map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    status: field.status === \"Ok\"\n                }));\n            const existingFieldComments = training.procedureFields.nodes.filter((field)=>field.comment).map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    comment: field.comment\n                }));\n            setBufferProcedureCheck(existingProcedureChecks);\n            setBufferFieldComment(existingFieldComments);\n        }\n    }, [\n        trainingID\n    ]);\n    const handleSetVessels = (data)=>{\n        const activeVessels = data === null || data === void 0 ? void 0 : data.filter((vessel)=>!vessel.archived);\n        const formattedData = [\n            {\n                label: \"Other\",\n                value: \"Other\"\n            },\n            {\n                label: \"Desktop/shore\",\n                value: \"Onshore\"\n            },\n            ...activeVessels.map((vessel)=>({\n                    value: vessel.id,\n                    label: vessel.title\n                }))\n        ];\n        setVessels(formattedData);\n    };\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_28__.ReadVessels, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isLoading) {\n            const loadVessels = async ()=>{\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            };\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        queryVessels\n    ]);\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                router.push(\"/crew-training\");\n            } else {\n                console.error(\"mutationCreateUser error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                // Handle procedure checks for updates\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields, _bufferFieldComment_find;\n                        const existingField = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((field)=>field.customisedComponentFieldID === procedureField.fieldId);\n                        return {\n                            id: existingField === null || existingField === void 0 ? void 0 : existingField.id,\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        if (procedureField.id) {\n                            // Update existing field\n                            updateCustomisedComponentFieldData({\n                                variables: {\n                                    input: procedureField\n                                }\n                            });\n                        } else {\n                            // Create new field\n                            const { id, ...createInput } = procedureField;\n                            createCustomisedComponentFieldData({\n                                variables: {\n                                    input: createInput\n                                }\n                            });\n                        }\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n                if (+memberId > 0) {\n                    router.push(\"/crew/info?id=\".concat(memberId));\n                } else if (+vesselId > 0) {\n                    router.push(\"/vessel/info?id=\".concat(vesselId));\n                } else {\n                    router.push(\"/crew-training\");\n                }\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        const { data } = await readOneTrainingSessionDue({\n            variables: variables\n        });\n        onCompleted(data.readOneTrainingSessionDue);\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    await mutationCreateTrainingSessionDue(variables);\n                } else {\n                    await mutationUpdateTrainingSessionDue(variables);\n                }\n            }));\n        }\n    };\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            toast({\n                title: \"Error\",\n                description: errors.TrainingTypes || errors.TrainerID || errors.VesselID || errors.Date,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\"\n        };\n        if (trainingID === 0) {\n            await mutationCreateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        } else {\n            await mutationUpdateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        }\n    }, [\n        training,\n        trainingID,\n        content,\n        bufferProcedureCheck,\n        bufferFieldComment,\n        rawTraining,\n        memberId,\n        vesselId,\n        mutationCreateTrainingSession,\n        mutationUpdateTrainingSession,\n        router,\n        toast\n    ]);\n    // var signatureCount = 0\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        await queryGetMemberTrainingSignatures({\n            variables: {\n                filter: {\n                    memberID: {\n                        eq: signature.MemberID\n                    },\n                    trainingSessionID: {\n                        in: TrainingID\n                    }\n                }\n            }\n        }).then((response)=>{\n            const data = response.data.readMemberTraining_Signatures.nodes;\n            if (data.length > 0) {\n                mutationUpdateMemberTrainingSignature({\n                    variables: {\n                        input: {\n                            id: data[0].id,\n                            memberID: signature.MemberID,\n                            signatureData: signature.SignatureData,\n                            trainingSessionID: TrainingID\n                        }\n                    }\n                });\n            } else {\n                if (signature.SignatureData) {\n                    mutationCreateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"mutationGetMemberTrainingSignatures error\", error);\n        });\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((date)=>{\n        setTrainingDate(date && new Date(date.toString()));\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n            }));\n    }, []);\n    const handleTrainerChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((trainer)=>{\n        if (!trainer) return; // Add early return if trainer is null\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        setTraining((prevTraining)=>{\n            const membersSet = new Set((prevTraining === null || prevTraining === void 0 ? void 0 : prevTraining.Members) || []);\n            membersSet.add(trainer.value);\n            const members = Array.from(membersSet);\n            return {\n                ...prevTraining,\n                TrainerID: trainer.value,\n                Members: members\n            };\n        });\n        setSelectedMemberList((prevList)=>[\n                ...prevList,\n                trainer\n            ]);\n        setSignatureMembers((prevSignatures)=>[\n                ...prevSignatures,\n                {\n                    MemberID: +trainer.value,\n                    SignatureData: null\n                }\n            ]);\n    }, []);\n    const handleTrainingTypeChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((trainingTypes)=>{\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                TrainingTypes: trainingTypes.map((item)=>item.value)\n            }));\n    }, []);\n    /* const handleTrainingLocationChange = (vessel: any) => {\r\n        setTraining({\r\n            ...training,\r\n            VesselID: vessel.isVessel ? vessel.value : 0,\r\n            TrainingLocationID: !vessel.isVessel ? vessel.value : 0,\r\n        })\r\n    } */ const handleMemberChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((members)=>{\n        console.log(\"\\uD83D\\uDD27 TrainingForm - handleMemberChange called with:\", {\n            members,\n            membersLength: members === null || members === void 0 ? void 0 : members.length,\n            membersType: typeof members\n        });\n        const memberIds = members.map((item)=>item.value);\n        console.log(\"\\uD83D\\uDD27 TrainingForm - extracted member IDs:\", memberIds);\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                Members: memberIds\n            }));\n        setSelectedMemberList(members);\n        setSignatureMembers((prevSignatures)=>prevSignatures.filter((item)=>members.some((m)=>+m.value === item.MemberID)));\n    }, []);\n    const onSignatureChanged = (e, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (e) {\n            if (index !== -1) {\n                if (e.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = e;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: e\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    const handleTrainingVesselChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((vessel)=>{\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                VesselID: vessel ? typeof vessel === \"object\" && !Array.isArray(vessel) ? vessel.value : 0 : 0\n            }));\n    }, []);\n    const handleEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((newContent)=>{\n        setContent(newContent);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training)) {\n            const vid = vesselId > 0 || isNaN(parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10)) ? vesselId : parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10);\n            setVesselID(vid);\n        }\n    }, [\n        vesselId,\n        training\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.getPermissions);\n    }, []);\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 935,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 937,\n            columnNumber: 13\n        }, undefined);\n    }\n    const procedures = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        const filteredProcedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return filteredProcedures.map((type)=>{\n            var _this;\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: (_this = [\n                    ...type.customisedComponentField.nodes\n                ]) === null || _this === void 0 ? void 0 : _this.sort((a, b)=>a.sortOrder - b.sortOrder)\n            } : null;\n        }).filter((type)=>type != null);\n    }, [\n        trainingTypes,\n        training === null || training === void 0 ? void 0 : training.TrainingTypes\n    ]);\n    const vesselOptions = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        if (!vessels) return [];\n        return vessels.map((vessel)=>({\n                label: vessel.label,\n                value: vessel.value\n            }));\n    }, [\n        vessels\n    ]);\n    const crewValue = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        return memberId > 0 ? [\n            memberId.toString()\n        ] : training === null || training === void 0 ? void 0 : training.Members;\n    }, [\n        memberId,\n        training === null || training === void 0 ? void 0 : training.Members\n    ]);\n    const hasTrainingProcedures = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        return training && trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n        }).length > 0;\n    }, [\n        training,\n        trainingTypes\n    ]);\n    const handleProcedureChecks = (field, type, status)=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n        setBufferProcedureCheck([\n            ...procedureCheck,\n            {\n                fieldId: field.id,\n                status: status\n            }\n        ]);\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n        setBufferFieldComment([\n            ...fieldComment,\n            {\n                fieldId: currentField.id,\n                comment: currentComment\n            }\n        ]);\n        setOpenCommentAlert(false);\n    }, [\n        bufferFieldComment,\n        currentField,\n        currentComment\n    ]);\n    const handleViewProcedures = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        setOpenViewProcedure(true);\n    }, []);\n    const handleCancelClick = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        router.push(\"/crew-training\");\n    }, [\n        router\n    ]);\n    const handleCommentChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((e)=>{\n        setCurrentComment(e.target.value);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                className: \"mb-2.5 mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardTitle, {\n                            children: [\n                                trainingID === 0 ? \"New\" : \"Edit\",\n                                \" Training Session\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1073,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1072,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                        className: \"my-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1077,\n                        columnNumber: 17\n                    }, undefined),\n                    !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1079,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                        className: \"p-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 my-4 \",\n                                        children: [\n                                            \"Training Details\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \" mt-4 max-w-[25rem] leading-loose mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1085,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            hasTrainingProcedures && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                                onClick: handleViewProcedures,\n                                                children: \"View Procedures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1087,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1083,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-2 pt-8 pb-5 space-y-6 px-7 border border-border border-dashed rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full my-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                label: \"Trainer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                                                    vesselID: vesselID,\n                                                                    onChange: handleTrainerChange\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                    lineNumber: 1096,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1095,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.TrainerID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1102,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1094,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full md:mt-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                                                onChange: handleTrainingTypeChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1108,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-red-vivid-500\",\n                                                                children: hasFormErrors && formErrors.TrainingTypes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1112,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1107,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1093,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full mt-4 flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                        children: \"Crew\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1119,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        value: crewValue,\n                                                        vesselID: vesselID,\n                                                        onChange: handleMemberChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1118,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                                                className: \"my-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1126,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex w-full gap-4 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full \",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                mode: \"single\",\n                                                                onChange: handleTrainingDateChange,\n                                                                value: new Date(trainingDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1133,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.Date\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1138,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1128,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            vessels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                                                options: vesselOptions,\n                                                                defaultValues: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_VesselID = rawTraining.VesselID) === null || _rawTraining_VesselID === void 0 ? void 0 : _rawTraining_VesselID.toString() : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? \"Desktop/shore\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? \"Other\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.id.toString() : vesselId ? {\n                                                                    label: (_vessels_find = vessels.find((vessel)=>vessel.value === vesselId)) === null || _vessels_find === void 0 ? void 0 : _vessels_find.label,\n                                                                    value: vesselId.toString()\n                                                                } : null,\n                                                                isLoading: rawTraining,\n                                                                onChange: handleTrainingVesselChange,\n                                                                placeholder: \"Select location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1144,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.VesselID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1183,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1142,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1127,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3 md:col-span-2\",\n                                                children: [\n                                                    procedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: procedures.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.CheckField, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        label: type.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1203,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.CheckFieldContent, {\n                                                                        children: type.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.DailyCheckField, {\n                                                                                displayField: field.status === \"Required\",\n                                                                                displayDescription: field.description,\n                                                                                displayLabel: field.fieldName,\n                                                                                inputId: field.id,\n                                                                                handleNoChange: ()=>handleProcedureChecks(field, type, false),\n                                                                                defaultNoChecked: getFieldStatus(field) === \"Not_Ok\",\n                                                                                handleYesChange: ()=>handleProcedureChecks(field, type, true),\n                                                                                defaultYesChecked: getFieldStatus(field) === \"Ok\",\n                                                                                commentAction: ()=>showCommentPopup(field),\n                                                                                comment: getComment(field),\n                                                                                displayImage: trainingID > 0,\n                                                                                fieldImages: fieldImages,\n                                                                                onImageUpload: refreshImages,\n                                                                                sectionData: {\n                                                                                    id: trainingID,\n                                                                                    sectionName: \"trainingSessionID\"\n                                                                                }\n                                                                            }, field.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                                lineNumber: 1207,\n                                                                                columnNumber: 65\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1204,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, type.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1202,\n                                                                columnNumber: 49\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1200,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"my-4 flex items-center w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            id: \"TrainingSummary\",\n                                                            placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                                                            className: \"!w-full  ring-1 ring-inset \",\n                                                            handleEditorChange: handleEditorChange,\n                                                            content: content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1282,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1198,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1092,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1082,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                                className: \"my-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1303,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 md:my-4 \",\n                                        children: \"Signatures\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1305,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 sm:col-span-2 md:my-4 flex justify-between flex-wrap gap-4\",\n                                        children: selectedMemberList && selectedMemberList.map((member, index)=>{\n                                            var _signatureMembers_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full md:w-96\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-full\",\n                                                    member: member.label,\n                                                    memberId: member.value,\n                                                    onSignatureChanged: (signature, member, memberId)=>onSignatureChanged(signature, member !== null && member !== void 0 ? member : \"\", memberId || 0),\n                                                    signature: {\n                                                        id: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.ID\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1315,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1312,\n                                                columnNumber: 45\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1308,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1304,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1081,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1071,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_21__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        variant: \"back\",\n                        onClick: handleCancelClick,\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1347,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        onClick: handleSave,\n                        disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                        children: trainingID === 0 ? \"Create session\" : \"Update session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1359,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1346,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[400px] sm:w-[540px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1371,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1370,\n                            columnNumber: 21\n                        }, undefined),\n                        training && trainingTypes.filter((type)=>{\n                            var _training_TrainingTypes;\n                            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                        }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 px-2.5 sm:px-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_25__.H4, {\n                                        children: type.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1386,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: type.procedure\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1388,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, type.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1383,\n                                columnNumber: 33\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1369,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1368,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialog, {\n                open: openCommentAlert,\n                onOpenChange: setOpenCommentAlert,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogTitle, {\n                                    children: \"Add Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1402,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogDescription, {\n                                    children: \"Add a comment for this procedure check.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1403,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1401,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_27__.Textarea, {\n                            value: currentComment,\n                            onChange: handleCommentChange,\n                            placeholder: \"Enter your comment here...\",\n                            rows: 4\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1407,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1414,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogAction, {\n                                    onClick: handleSaveComment,\n                                    children: \"Save Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1415,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1413,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1400,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1397,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TrainingForm, \"Ibssvur7yh/qWBbk9D//K9ER5qk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation\n    ];\n});\n_c = TrainingForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingForm);\nvar _c;\n$RefreshReg$(_c, \"TrainingForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx\n"));

/***/ })

});