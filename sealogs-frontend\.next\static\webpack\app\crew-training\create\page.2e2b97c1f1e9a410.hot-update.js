"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx":
/*!**************************************************!*\
  !*** ./src/app/ui/crew-training/create/form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew-training/create/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TrainingForm = (param)=>{\n    let { trainingID = 0, memberId = 0, trainingTypeId = 0, vesselId = 0 } = param;\n    var _rawTraining_VesselID, _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _vessels_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Date());\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [vesselID, setVesselID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(vesselId);\n    const [fieldImages, setFieldImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // GraphQL queries to replace the problematic function calls\n    const [queryTrainingTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.CREW_TRAINING_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingTypes.nodes;\n            if (data) {\n                setTrainingTypes(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingTypes error\", error);\n        }\n    });\n    const [queryTrainingSessionByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.TRAINING_SESSION_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingSession;\n            if (data) {\n                handleSetTraining(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingSession error\", error);\n        }\n    });\n    const [queryTrainingTypeByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.TRAINING_TYPE_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingType;\n            if (data) {\n                setTraining((prevTraining)=>({\n                        ...prevTraining,\n                        TrainingTypes: [\n                            data.id\n                        ]\n                    }));\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingType error\", error);\n        }\n    });\n    const [getFieldImages] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_SECTION_MEMBER_IMAGES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCaptureImages.nodes;\n            if (data) {\n                setFieldImages(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getFieldImages error\", error);\n        }\n    });\n    // Define handleSetTraining function\n    const handleSetTraining = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((training)=>{\n        var _training_procedureFields;\n        const tDate = new Date(training.date);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.date).format(\"YYYY-MM-DD\"),\n            Members: training.members.nodes.map((m)=>m.id),\n            TrainerID: training.trainer.id,\n            TrainingSummary: training.trainingSummary,\n            TrainingTypes: training.trainingTypes.nodes.map((t)=>t.id),\n            // VesselID: training.vessel.id,\n            VesselID: training.vesselID\n        };\n        const members = training.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        const vesselCrewIds = training.vessel.seaLogsMembers.nodes.map((slm)=>+slm.id);\n        const vesselCrews = members.filter((m)=>vesselCrewIds.includes(+m.value));\n        const signatures = training.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        // Batch all state updates using React's automatic batching\n        setTrainingDate(tDate);\n        setRawTraining(training);\n        setTraining(trainingData);\n        setContent(training.trainingSummary);\n        setSelectedMemberList(vesselCrews);\n        setSignatureMembers(signatures);\n        // Initialize buffer with existing procedure field data for updates\n        if ((_training_procedureFields = training.procedureFields) === null || _training_procedureFields === void 0 ? void 0 : _training_procedureFields.nodes) {\n            const existingProcedureChecks = training.procedureFields.nodes.map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    status: field.status === \"Ok\"\n                }));\n            const existingFieldComments = training.procedureFields.nodes.filter((field)=>field.comment).map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    comment: field.comment\n                }));\n            setBufferProcedureCheck(existingProcedureChecks);\n            setBufferFieldComment(existingFieldComments);\n        }\n    }, [\n        trainingID\n    ]);\n    // useEffect hooks to replace the problematic function calls\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training types on component mount\n        queryTrainingTypes();\n    }, []) // Empty dependency array - only run once on mount\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training session by ID when trainingID changes\n        if (trainingID > 0) {\n            queryTrainingSessionByID({\n                variables: {\n                    id: trainingID\n                }\n            });\n        }\n    }, [\n        trainingID\n    ]) // Only depend on trainingID\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training type by ID when trainingTypeId changes\n        if (trainingTypeId > 0) {\n            queryTrainingTypeByID({\n                variables: {\n                    id: trainingTypeId\n                }\n            });\n        }\n    }, [\n        trainingTypeId\n    ]) // Only depend on trainingTypeId\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (trainingID > 0) {\n            getFieldImages({\n                variables: {\n                    filter: {\n                        trainingSessionID: {\n                            eq: trainingID\n                        }\n                    }\n                }\n            });\n        }\n    }, [\n        trainingID\n    ]) // Only depend on trainingID\n    ;\n    const refreshImages = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async ()=>{\n        await getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    }, [\n        getFieldImages,\n        trainingID\n    ]);\n    const handleSetVessels = (data)=>{\n        const activeVessels = data === null || data === void 0 ? void 0 : data.filter((vessel)=>!vessel.archived);\n        const formattedData = [\n            {\n                label: \"Other\",\n                value: \"Other\"\n            },\n            {\n                label: \"Desktop/shore\",\n                value: \"Onshore\"\n            },\n            ...activeVessels.map((vessel)=>({\n                    value: vessel.id,\n                    label: vessel.title\n                }))\n        ];\n        setVessels(formattedData);\n    };\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_28__.ReadVessels, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isLoading) {\n            const loadVessels = async ()=>{\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            };\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        queryVessels\n    ]);\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                router.push(\"/crew-training\");\n            } else {\n                console.error(\"mutationCreateUser error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                // Handle procedure checks for updates\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields, _bufferFieldComment_find;\n                        const existingField = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((field)=>field.customisedComponentFieldID === procedureField.fieldId);\n                        return {\n                            id: existingField === null || existingField === void 0 ? void 0 : existingField.id,\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        if (procedureField.id) {\n                            // Update existing field\n                            updateCustomisedComponentFieldData({\n                                variables: {\n                                    input: procedureField\n                                }\n                            });\n                        } else {\n                            // Create new field\n                            const { id, ...createInput } = procedureField;\n                            createCustomisedComponentFieldData({\n                                variables: {\n                                    input: createInput\n                                }\n                            });\n                        }\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n                if (+memberId > 0) {\n                    router.push(\"/crew/info?id=\".concat(memberId));\n                } else if (+vesselId > 0) {\n                    router.push(\"/vessel/info?id=\".concat(vesselId));\n                } else {\n                    router.push(\"/crew-training\");\n                }\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        const { data } = await readOneTrainingSessionDue({\n            variables: variables\n        });\n        onCompleted(data.readOneTrainingSessionDue);\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    await mutationCreateTrainingSessionDue(variables);\n                } else {\n                    await mutationUpdateTrainingSessionDue(variables);\n                }\n            }));\n        }\n    };\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            toast({\n                title: \"Error\",\n                description: errors.TrainingTypes || errors.TrainerID || errors.VesselID || errors.Date,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\"\n        };\n        if (trainingID === 0) {\n            await mutationCreateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        } else {\n            await mutationUpdateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        }\n    }, [\n        training,\n        trainingID,\n        content,\n        bufferProcedureCheck,\n        bufferFieldComment,\n        rawTraining,\n        memberId,\n        vesselId,\n        mutationCreateTrainingSession,\n        mutationUpdateTrainingSession,\n        router,\n        toast\n    ]);\n    // var signatureCount = 0\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        await queryGetMemberTrainingSignatures({\n            variables: {\n                filter: {\n                    memberID: {\n                        eq: signature.MemberID\n                    },\n                    trainingSessionID: {\n                        in: TrainingID\n                    }\n                }\n            }\n        }).then((response)=>{\n            const data = response.data.readMemberTraining_Signatures.nodes;\n            if (data.length > 0) {\n                mutationUpdateMemberTrainingSignature({\n                    variables: {\n                        input: {\n                            id: data[0].id,\n                            memberID: signature.MemberID,\n                            signatureData: signature.SignatureData,\n                            trainingSessionID: TrainingID\n                        }\n                    }\n                });\n            } else {\n                if (signature.SignatureData) {\n                    mutationCreateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"mutationGetMemberTrainingSignatures error\", error);\n        });\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((date)=>{\n        setTrainingDate(date && new Date(date.toString()));\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n            }));\n    }, []);\n    const handleTrainerChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((trainer)=>{\n        if (!trainer) return; // Add early return if trainer is null\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        setTraining((prevTraining)=>{\n            const membersSet = new Set((prevTraining === null || prevTraining === void 0 ? void 0 : prevTraining.Members) || []);\n            membersSet.add(trainer.value);\n            const members = Array.from(membersSet);\n            return {\n                ...prevTraining,\n                TrainerID: trainer.value,\n                Members: members\n            };\n        });\n        setSelectedMemberList((prevList)=>[\n                ...prevList,\n                trainer\n            ]);\n        setSignatureMembers((prevSignatures)=>[\n                ...prevSignatures,\n                {\n                    MemberID: +trainer.value,\n                    SignatureData: null\n                }\n            ]);\n    }, []);\n    const handleTrainingTypeChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((trainingTypes)=>{\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                TrainingTypes: trainingTypes.map((item)=>item.value)\n            }));\n    }, []);\n    /* const handleTrainingLocationChange = (vessel: any) => {\r\n        setTraining({\r\n            ...training,\r\n            VesselID: vessel.isVessel ? vessel.value : 0,\r\n            TrainingLocationID: !vessel.isVessel ? vessel.value : 0,\r\n        })\r\n    } */ const handleMemberChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((members)=>{\n        console.log(\"\\uD83D\\uDD27 TrainingForm - handleMemberChange called with:\", {\n            members,\n            membersLength: members === null || members === void 0 ? void 0 : members.length,\n            membersType: typeof members\n        });\n        const memberIds = members.map((item)=>item.value);\n        console.log(\"\\uD83D\\uDD27 TrainingForm - extracted member IDs:\", memberIds);\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                Members: memberIds\n            }));\n        setSelectedMemberList(members);\n        setSignatureMembers((prevSignatures)=>prevSignatures.filter((item)=>members.some((m)=>+m.value === item.MemberID)));\n    }, []);\n    const onSignatureChanged = (e, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (e) {\n            if (index !== -1) {\n                if (e.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = e;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: e\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    const handleTrainingVesselChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((vessel)=>{\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                VesselID: vessel ? typeof vessel === \"object\" && !Array.isArray(vessel) ? vessel.value : 0 : 0\n            }));\n    }, []);\n    const handleEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((newContent)=>{\n        setContent(newContent);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training)) {\n            const vid = vesselId > 0 || isNaN(parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10)) ? vesselId : parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10);\n            setVesselID(vid);\n        }\n    }, [\n        vesselId,\n        training\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.getPermissions);\n    }, []);\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 936,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 938,\n            columnNumber: 13\n        }, undefined);\n    }\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            var _this;\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: (_this = [\n                    ...type.customisedComponentField.nodes\n                ]) === null || _this === void 0 ? void 0 : _this.sort((a, b)=>a.sortOrder - b.sortOrder)\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const crewValue = useMemo(()=>{\n        return memberId > 0 ? [\n            memberId.toString()\n        ] : training === null || training === void 0 ? void 0 : training.Members;\n    }, [\n        memberId,\n        training === null || training === void 0 ? void 0 : training.Members\n    ]);\n    const hasTrainingProcedures = useMemo(()=>{\n        return training && trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n        }).length > 0;\n    }, [\n        training,\n        trainingTypes\n    ]);\n    const handleProcedureChecks = (field, type, status)=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n        setBufferProcedureCheck([\n            ...procedureCheck,\n            {\n                fieldId: field.id,\n                status: status\n            }\n        ]);\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n        setBufferFieldComment([\n            ...fieldComment,\n            {\n                fieldId: currentField.id,\n                comment: currentComment\n            }\n        ]);\n        setOpenCommentAlert(false);\n    }, [\n        bufferFieldComment,\n        currentField,\n        currentComment\n    ]);\n    const handleViewProcedures = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        setOpenViewProcedure(true);\n    }, []);\n    const handleCancelClick = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        router.push(\"/crew-training\");\n    }, [\n        router\n    ]);\n    const handleCommentChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((e)=>{\n        setCurrentComment(e.target.value);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                className: \"mb-2.5 mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardTitle, {\n                            children: [\n                                trainingID === 0 ? \"New\" : \"Edit\",\n                                \" Training Session\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1066,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1065,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                        className: \"my-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1070,\n                        columnNumber: 17\n                    }, undefined),\n                    !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1072,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                        className: \"p-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 my-4 \",\n                                        children: [\n                                            \"Training Details\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \" mt-4 max-w-[25rem] leading-loose mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1078,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            hasTrainingProcedures && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                                onClick: handleViewProcedures,\n                                                children: \"View Procedures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1080,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1076,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-2 pt-8 pb-5 space-y-6 px-7 border border-border border-dashed rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full my-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                label: \"Trainer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                                                    vesselID: vesselID,\n                                                                    onChange: handleTrainerChange\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                    lineNumber: 1089,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1088,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.TrainerID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1095,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1087,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full md:mt-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                                                onChange: handleTrainingTypeChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1101,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-red-vivid-500\",\n                                                                children: hasFormErrors && formErrors.TrainingTypes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1105,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1086,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full mt-4 flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                        children: \"Crew\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1112,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        value: crewValue,\n                                                        vesselID: vesselID,\n                                                        onChange: handleMemberChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1113,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1111,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                                                className: \"my-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1119,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex w-full gap-4 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full \",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                mode: \"single\",\n                                                                onChange: handleTrainingDateChange,\n                                                                value: new Date(trainingDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1126,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.Date\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1131,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1121,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            vessels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                                                options: vessels.map((vessel)=>({\n                                                                        label: vessel.label,\n                                                                        value: vessel.value\n                                                                    })),\n                                                                defaultValues: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_VesselID = rawTraining.VesselID) === null || _rawTraining_VesselID === void 0 ? void 0 : _rawTraining_VesselID.toString() : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? \"Desktop/shore\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? \"Other\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.id.toString() : vesselId ? {\n                                                                    label: (_vessels_find = vessels.find((vessel)=>vessel.value === vesselId)) === null || _vessels_find === void 0 ? void 0 : _vessels_find.label,\n                                                                    value: vesselId.toString()\n                                                                } : null,\n                                                                isLoading: rawTraining,\n                                                                onChange: handleTrainingVesselChange,\n                                                                placeholder: \"Select location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1137,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.VesselID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1181,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1120,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3 md:col-span-2\",\n                                                children: [\n                                                    getProcedures().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: getProcedures().map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.CheckField, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        label: type.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1202,\n                                                                        columnNumber: 57\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.CheckFieldContent, {\n                                                                        children: type.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.DailyCheckField, {\n                                                                                displayField: field.status === \"Required\",\n                                                                                displayDescription: field.description,\n                                                                                displayLabel: field.fieldName,\n                                                                                inputId: field.id,\n                                                                                handleNoChange: ()=>handleProcedureChecks(field, type, false),\n                                                                                defaultNoChecked: getFieldStatus(field) === \"Not_Ok\",\n                                                                                handleYesChange: ()=>handleProcedureChecks(field, type, true),\n                                                                                defaultYesChecked: getFieldStatus(field) === \"Ok\",\n                                                                                commentAction: ()=>showCommentPopup(field),\n                                                                                comment: getComment(field),\n                                                                                displayImage: trainingID > 0,\n                                                                                fieldImages: fieldImages,\n                                                                                onImageUpload: refreshImages,\n                                                                                sectionData: {\n                                                                                    id: trainingID,\n                                                                                    sectionName: \"trainingSessionID\"\n                                                                                }\n                                                                            }, field.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                                lineNumber: 1210,\n                                                                                columnNumber: 69\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1205,\n                                                                        columnNumber: 57\n                                                                    }, undefined)\n                                                                ]\n                                                            }, type.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1201,\n                                                                columnNumber: 53\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1198,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"my-4 flex items-center w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            id: \"TrainingSummary\",\n                                                            placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                                                            className: \"!w-full  ring-1 ring-inset \",\n                                                            handleEditorChange: handleEditorChange,\n                                                            content: content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1286,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1285,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1196,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1085,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1075,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                                className: \"my-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1307,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 md:my-4 \",\n                                        children: \"Signatures\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1309,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 sm:col-span-2 md:my-4 flex justify-between flex-wrap gap-4\",\n                                        children: selectedMemberList && selectedMemberList.map((member, index)=>{\n                                            var _signatureMembers_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full md:w-96\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-full\",\n                                                    member: member.label,\n                                                    memberId: member.value,\n                                                    onSignatureChanged: (signature, member, memberId)=>onSignatureChanged(signature, member !== null && member !== void 0 ? member : \"\", memberId || 0),\n                                                    signature: {\n                                                        id: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.ID\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1319,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1316,\n                                                columnNumber: 45\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1312,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1308,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1074,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1064,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_21__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        variant: \"back\",\n                        onClick: handleCancelClick,\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1351,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        onClick: handleSave,\n                        disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                        children: trainingID === 0 ? \"Create session\" : \"Update session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1363,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1350,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[400px] sm:w-[540px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1375,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1374,\n                            columnNumber: 21\n                        }, undefined),\n                        training && trainingTypes.filter((type)=>{\n                            var _training_TrainingTypes;\n                            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                        }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 px-2.5 sm:px-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_25__.H4, {\n                                        children: type.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1390,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: type.procedure\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1392,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, type.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1387,\n                                columnNumber: 33\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1373,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1372,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialog, {\n                open: openCommentAlert,\n                onOpenChange: setOpenCommentAlert,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogTitle, {\n                                    children: \"Add Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1406,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogDescription, {\n                                    children: \"Add a comment for this procedure check.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1407,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1405,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_27__.Textarea, {\n                            value: currentComment,\n                            onChange: handleCommentChange,\n                            placeholder: \"Enter your comment here...\",\n                            rows: 4\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1411,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1418,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogAction, {\n                                    onClick: handleSaveComment,\n                                    children: \"Save Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1419,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1417,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1404,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1401,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TrainingForm, \"YPDAnCJt4Lnn9C183sfXHcuTV7M=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation\n    ];\n});\n_c = TrainingForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingForm);\nvar _c;\n$RefreshReg$(_c, \"TrainingForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy10cmFpbmluZy9jcmVhdGUvZm9ybS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFeUI7QUFDTztBQUN3QjtBQUN1QztBQUNqQjtBQUNKO0FBQ2dCO0FBVXZEO0FBUUg7QUFDMEI7QUFDZjtBQUNWO0FBRXVDO0FBQ3JDO0FBQ1U7QUFDYztBQUNvQjtBQUMxQjtBQU12QjtBQUNpQjtBQUNNO0FBQ007QUFNcEI7QUFDUTtBQUNIO0FBQ0o7QUFDSjtBQVVDO0FBQ2M7QUFDWjtBQUV2QyxNQUFNNkQsZUFBZTtRQUFDLEVBQ2xCQyxhQUFhLENBQUMsRUFDZEMsV0FBVyxDQUFDLEVBQ1pDLGlCQUFpQixDQUFDLEVBQ2xCQyxXQUFXLENBQUMsRUFNZjtRQTJpQ3lEQyx1QkFTTUEsK0JBR0FBLGdDQUdhQzs7SUF6akN6RSxNQUFNQyxTQUFTM0MsMkRBQVNBO0lBQ3hCLE1BQU0sRUFBRTRDLEtBQUssRUFBRSxHQUFHckIsMkRBQVFBO0lBQzFCLE1BQU0sQ0FBQ3NCLFdBQVdDLGFBQWEsR0FBR3BFLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3FFLFVBQVVDLFlBQVksR0FBR3RFLCtDQUFRQSxDQUFNLENBQUM7SUFDL0MsTUFBTSxDQUFDK0QsYUFBYVEsZUFBZSxHQUFHdkUsK0NBQVFBO0lBQzlDLE1BQU0sQ0FBQ3dFLGNBQWNDLGdCQUFnQixHQUFHekUsK0NBQVFBLENBQUMsSUFBSTBFO0lBQ3JELE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUc1RSwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUM2RSxvQkFBb0JDLHNCQUFzQixHQUFHOUUsK0NBQVFBLENBQUMsRUFBRTtJQUMvRCxNQUFNLENBQUMrRSxrQkFBa0JDLG9CQUFvQixHQUFHaEYsK0NBQVFBLENBQUMsRUFBRTtJQUMzRCxNQUFNLENBQUNnRSxTQUFTaUIsV0FBVyxHQUFHakYsK0NBQVFBO0lBQ3RDLE1BQU0sQ0FBQ2tGLGVBQWVDLGlCQUFpQixHQUFHbkYsK0NBQVFBLENBQU0sRUFBRTtJQUMxRCxNQUFNLENBQUNvRixTQUFTQyxXQUFXLEdBQUdyRiwrQ0FBUUEsQ0FBTTtJQUM1QyxNQUFNLENBQUNzRixtQkFBbUJDLHFCQUFxQixHQUFHdkYsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDd0YsZ0JBQWdCQyxrQkFBa0IsR0FBR3pGLCtDQUFRQSxDQUFNO0lBQzFELE1BQU0sQ0FBQzBGLGNBQWNDLGdCQUFnQixHQUFHM0YsK0NBQVFBLENBQU07SUFDdEQsTUFBTSxDQUFDNEYsc0JBQXNCQyx3QkFBd0IsR0FBRzdGLCtDQUFRQSxDQUFNLEVBQUU7SUFDeEUsTUFBTSxDQUFDOEYsb0JBQW9CQyxzQkFBc0IsR0FBRy9GLCtDQUFRQSxDQUFNLEVBQUU7SUFDcEUsTUFBTSxDQUFDZ0csa0JBQWtCQyxvQkFBb0IsR0FBR2pHLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ2tHLFlBQVlDLGNBQWMsR0FBR25HLCtDQUFRQSxDQUFDO1FBQ3pDb0csZUFBZTtRQUNmQyxXQUFXO1FBQ1hDLFVBQVU7UUFDVjVCLE1BQU07SUFDVjtJQUNBLE1BQU0sQ0FBQzZCLFVBQVVDLFlBQVksR0FBR3hHLCtDQUFRQSxDQUFDOEQ7SUFDekMsTUFBTSxDQUFDMkMsYUFBYUMsZUFBZSxHQUFHMUcsK0NBQVFBLENBQU07SUFFcEQsNERBQTREO0lBQzVELE1BQU0sQ0FBQzJHLG1CQUFtQixHQUFHdEYsNkRBQVlBLENBQUNKLHVFQUFtQkEsRUFBRTtRQUMzRDJGLGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWLE1BQU1DLE9BQU9ELFNBQVNFLGlCQUFpQixDQUFDQyxLQUFLO1lBQzdDLElBQUlGLE1BQU07Z0JBQ041QixpQkFBaUI0QjtZQUNyQjtRQUNKO1FBQ0FHLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDOUM7SUFDSjtJQUVBLE1BQU0sQ0FBQ0UseUJBQXlCLEdBQUdoRyw2REFBWUEsQ0FBQ0gsMEVBQXNCQSxFQUFFO1FBQ3BFMEYsYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1YsTUFBTUMsT0FBT0QsU0FBU1Esc0JBQXNCO1lBQzVDLElBQUlQLE1BQU07Z0JBQ05RLGtCQUFrQlI7WUFDdEI7UUFDSjtRQUNBRyxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1FBQ2hEO0lBQ0o7SUFFQSxNQUFNLENBQUNLLHNCQUFzQixHQUFHbkcsNkRBQVlBLENBQUNGLHVFQUFtQkEsRUFBRTtRQUM5RHlGLGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWLE1BQU1DLE9BQU9ELFNBQVNXLG1CQUFtQjtZQUN6QyxJQUFJVixNQUFNO2dCQUNOekMsWUFBWSxDQUFDb0QsZUFBdUI7d0JBQ2hDLEdBQUdBLFlBQVk7d0JBQ2Z0QixlQUFlOzRCQUFDVyxLQUFLWSxFQUFFO3lCQUFDO29CQUM1QjtZQUNKO1FBQ0o7UUFDQVQsU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtRQUM3QztJQUNKO0lBRUEsTUFBTSxDQUFDUyxlQUFlLEdBQUd2Ryw2REFBWUEsQ0FBQ04sNkVBQXlCQSxFQUFFO1FBQzdENkYsYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1YsTUFBTUMsT0FBT0QsU0FBU2UsaUJBQWlCLENBQUNaLEtBQUs7WUFDN0MsSUFBSUYsTUFBTTtnQkFDTkwsZUFBZUs7WUFDbkI7UUFDSjtRQUNBRyxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQyx3QkFBd0JBO1FBQzFDO0lBQ0o7SUFFQSxvQ0FBb0M7SUFDcEMsTUFBTUksb0JBQW9CdEgsa0RBQVdBLENBQ2pDLENBQUNvRTtZQTJDT0E7UUExQ0osTUFBTXlELFFBQVEsSUFBSXBELEtBQUtMLFNBQVMwRCxJQUFJO1FBQ3BDLE1BQU1DLGVBQWU7WUFDakJDLElBQUl0RTtZQUNKZSxNQUFNN0UsNENBQUtBLENBQUN3RSxTQUFTMEQsSUFBSSxFQUFFRyxNQUFNLENBQUM7WUFDbENDLFNBQVM5RCxTQUFTK0QsT0FBTyxDQUFDbkIsS0FBSyxDQUFDb0IsR0FBRyxDQUFDLENBQUNDLElBQVdBLEVBQUVYLEVBQUU7WUFDcER0QixXQUFXaEMsU0FBU2tFLE9BQU8sQ0FBQ1osRUFBRTtZQUM5QmEsaUJBQWlCbkUsU0FBU29FLGVBQWU7WUFDekNyQyxlQUFlL0IsU0FBU2EsYUFBYSxDQUFDK0IsS0FBSyxDQUFDb0IsR0FBRyxDQUMzQyxDQUFDSyxJQUFXQSxFQUFFZixFQUFFO1lBRXBCLGdDQUFnQztZQUNoQ3JCLFVBQVVqQyxTQUFTa0MsUUFBUTtRQUMvQjtRQUVBLE1BQU02QixVQUNGL0QsU0FBUytELE9BQU8sQ0FBQ25CLEtBQUssQ0FBQ29CLEdBQUcsQ0FBQyxDQUFDQztnQkFDZEEsY0FBcUJBO21CQURLO2dCQUNwQ0ssT0FBTyxHQUF3QkwsT0FBckJBLENBQUFBLGVBQUFBLEVBQUVNLFNBQVMsY0FBWE4sMEJBQUFBLGVBQWUsSUFBRyxLQUFtQixPQUFoQkEsQ0FBQUEsYUFBQUEsRUFBRU8sT0FBTyxjQUFUUCx3QkFBQUEsYUFBYTtnQkFDNUNRLE9BQU9SLEVBQUVYLEVBQUU7WUFDZjtRQUFBLE1BQU8sRUFBRTtRQUViLE1BQU1vQixnQkFBZ0IxRSxTQUFTMkUsTUFBTSxDQUFDQyxjQUFjLENBQUNoQyxLQUFLLENBQUNvQixHQUFHLENBQzFELENBQUNhLE1BQWEsQ0FBQ0EsSUFBSXZCLEVBQUU7UUFFekIsTUFBTXdCLGNBQWNmLFFBQVFnQixNQUFNLENBQUMsQ0FBQ2QsSUFDaENTLGNBQWNNLFFBQVEsQ0FBQyxDQUFDZixFQUFFUSxLQUFLO1FBR25DLE1BQU1RLGFBQWFqRixTQUFTaUYsVUFBVSxDQUFDckMsS0FBSyxDQUFDb0IsR0FBRyxDQUFDLENBQUNrQixJQUFZO2dCQUMxREMsVUFBVUQsRUFBRUUsTUFBTSxDQUFDOUIsRUFBRTtnQkFDckIrQixlQUFlSCxFQUFFSSxhQUFhO2dCQUM5QjFCLElBQUlzQixFQUFFNUIsRUFBRTtZQUNaO1FBRUEsMkRBQTJEO1FBQzNEbEQsZ0JBQWdCcUQ7UUFDaEJ2RCxlQUFlRjtRQUNmQyxZQUFZMEQ7UUFDWjNDLFdBQVdoQixTQUFTb0UsZUFBZTtRQUNuQzNELHNCQUFzQnFFO1FBQ3RCbkUsb0JBQW9Cc0U7UUFFcEIsbUVBQW1FO1FBQ25FLEtBQUlqRiw0QkFBQUEsU0FBU3VGLGVBQWUsY0FBeEJ2RixnREFBQUEsMEJBQTBCNEMsS0FBSyxFQUFFO1lBQ2pDLE1BQU00QywwQkFDRnhGLFNBQVN1RixlQUFlLENBQUMzQyxLQUFLLENBQUNvQixHQUFHLENBQUMsQ0FBQ3lCLFFBQWdCO29CQUNoREMsU0FBU0QsTUFBTUUsMEJBQTBCO29CQUN6Q0MsUUFBUUgsTUFBTUcsTUFBTSxLQUFLO2dCQUM3QjtZQUNKLE1BQU1DLHdCQUF3QjdGLFNBQVN1RixlQUFlLENBQUMzQyxLQUFLLENBQ3ZEbUMsTUFBTSxDQUFDLENBQUNVLFFBQWVBLE1BQU1LLE9BQU8sRUFDcEM5QixHQUFHLENBQUMsQ0FBQ3lCLFFBQWdCO29CQUNsQkMsU0FBU0QsTUFBTUUsMEJBQTBCO29CQUN6Q0csU0FBU0wsTUFBTUssT0FBTztnQkFDMUI7WUFFSnRFLHdCQUF3QmdFO1lBQ3hCOUQsc0JBQXNCbUU7UUFDMUI7SUFDSixHQUNBO1FBQUN2RztLQUFXO0lBR2hCLDREQUE0RDtJQUM1RDVELGdEQUFTQSxDQUFDO1FBQ04seUNBQXlDO1FBQ3pDNEc7SUFDSixHQUFHLEVBQUUsRUFBRSxrREFBa0Q7O0lBRXpENUcsZ0RBQVNBLENBQUM7UUFDTixzREFBc0Q7UUFDdEQsSUFBSTRELGFBQWEsR0FBRztZQUNoQjBELHlCQUF5QjtnQkFDckIrQyxXQUFXO29CQUNQekMsSUFBSWhFO2dCQUNSO1lBQ0o7UUFDSjtJQUNKLEdBQUc7UUFBQ0E7S0FBVyxFQUFFLDRCQUE0Qjs7SUFFN0M1RCxnREFBU0EsQ0FBQztRQUNOLHVEQUF1RDtRQUN2RCxJQUFJOEQsaUJBQWlCLEdBQUc7WUFDcEIyRCxzQkFBc0I7Z0JBQ2xCNEMsV0FBVztvQkFDUHpDLElBQUk5RDtnQkFDUjtZQUNKO1FBQ0o7SUFDSixHQUFHO1FBQUNBO0tBQWUsRUFBRSxnQ0FBZ0M7O0lBRXJEOUQsZ0RBQVNBLENBQUM7UUFDTixJQUFJNEQsYUFBYSxHQUFHO1lBQ2hCaUUsZUFBZTtnQkFDWHdDLFdBQVc7b0JBQ1BoQixRQUFRO3dCQUNKaUIsbUJBQW1COzRCQUFFQyxJQUFJM0c7d0JBQVc7b0JBQ3hDO2dCQUNKO1lBQ0o7UUFDSjtJQUNKLEdBQUc7UUFBQ0E7S0FBVyxFQUFFLDRCQUE0Qjs7SUFFN0MsTUFBTTRHLGdCQUFnQnRLLGtEQUFXQSxDQUFDO1FBQzlCLE1BQU0ySCxlQUFlO1lBQ2pCd0MsV0FBVztnQkFDUGhCLFFBQVE7b0JBQ0ppQixtQkFBbUI7d0JBQUVDLElBQUkzRztvQkFBVztnQkFDeEM7WUFDSjtRQUNKO0lBQ0osR0FBRztRQUFDaUU7UUFBZ0JqRTtLQUFXO0lBRS9CLE1BQU02RyxtQkFBbUIsQ0FBQ3pEO1FBQ3RCLE1BQU0wRCxnQkFBZ0IxRCxpQkFBQUEsMkJBQUFBLEtBQU1xQyxNQUFNLENBQUMsQ0FBQ0osU0FBZ0IsQ0FBQ0EsT0FBTzBCLFFBQVE7UUFDcEUsTUFBTUMsZ0JBQWdCO1lBQ2xCO2dCQUNJaEMsT0FBTztnQkFDUEcsT0FBTztZQUNYO1lBQ0E7Z0JBQ0lILE9BQU87Z0JBQ1BHLE9BQU87WUFDWDtlQUNHMkIsY0FBY3BDLEdBQUcsQ0FBQyxDQUFDVyxTQUFpQjtvQkFDbkNGLE9BQU9FLE9BQU9yQixFQUFFO29CQUNoQmdCLE9BQU9LLE9BQU80QixLQUFLO2dCQUN2QjtTQUNIO1FBQ0QzRixXQUFXMEY7SUFDZjtJQUVBLE1BQU0sQ0FBQ0UsYUFBYSxHQUFHeEosNkRBQVlBLENBQUNvQyxrREFBV0EsRUFBRTtRQUM3Q21ELGFBQWE7UUFDYkMsYUFBYSxDQUFDaUU7WUFDVixJQUFJQSxvQkFBb0JDLFdBQVcsQ0FBQzlELEtBQUssRUFBRTtnQkFDdkN1RCxpQkFBaUJNLG9CQUFvQkMsV0FBVyxDQUFDOUQsS0FBSztZQUMxRDtRQUNKO1FBQ0FDLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLHNCQUFzQkE7UUFDeEM7SUFDSjtJQUNBcEgsZ0RBQVNBLENBQUM7UUFDTixJQUFJb0UsV0FBVztZQUNYLE1BQU02RyxjQUFjO2dCQUNoQixNQUFNSCxhQUFhO29CQUNmVCxXQUFXO3dCQUNQYSxPQUFPO3dCQUNQQyxRQUFRO29CQUNaO2dCQUNKO1lBQ0o7WUFDQUY7WUFDQTVHLGFBQWE7UUFDakI7SUFDSixHQUFHO1FBQUNEO1FBQVcwRztLQUFhO0lBRTVCLE1BQU0sQ0FDRk0sK0JBQ0EsRUFBRUMsU0FBU0Msb0NBQW9DLEVBQUUsQ0FDcEQsR0FBR2pLLDREQUFXQSxDQUFDZCw4RUFBdUJBLEVBQUU7UUFDckN1RyxhQUFhLENBQUNDO1lBQ1YsTUFBTUMsT0FBT0QsU0FBU3dFLHFCQUFxQjtZQUMzQyxJQUFJdkUsS0FBS1ksRUFBRSxHQUFHLEdBQUc7Z0JBQ2IsSUFBSS9CLHFCQUFxQjJGLE1BQU0sR0FBRyxHQUFHO29CQUNqQyxNQUFNM0Isa0JBQWtCaEUscUJBQXFCeUMsR0FBRyxDQUM1QyxDQUFDbUQ7NEJBTWdCMUY7d0JBTGIsT0FBTzs0QkFDSG1FLFFBQVF1QixlQUFldkIsTUFBTSxHQUFHLE9BQU87NEJBQ3ZDSSxtQkFBbUJ0RCxLQUFLWSxFQUFFOzRCQUMxQnFDLDRCQUNJd0IsZUFBZXpCLE9BQU87NEJBQzFCSSxPQUFPLEdBQUVyRSwyQkFBQUEsbUJBQW1CMkYsSUFBSSxDQUM1QixDQUFDdEIsVUFDR0EsUUFBUUosT0FBTyxJQUNmeUIsZUFBZXpCLE9BQU8sZUFIckJqRSwrQ0FBQUEseUJBSU5xRSxPQUFPO3dCQUNkO29CQUNKO29CQUVKUCxnQkFBZ0I4QixPQUFPLENBQUMsQ0FBQ0Y7d0JBQ3JCRyxtQ0FBbUM7NEJBQy9CdkIsV0FBVztnQ0FDUHdCLE9BQU9KOzRCQUNYO3dCQUNKO29CQUNKO2dCQUNKO2dCQUNBSztnQkFDQUMsaUJBQWlCL0UsS0FBS1ksRUFBRTtnQkFDeEJvRSxtQkFBbUJoRixLQUFLMEIsZUFBZTtnQkFDdkN4RSxPQUFPK0gsSUFBSSxDQUFDO1lBQ2hCLE9BQU87Z0JBQ0g1RSxRQUFRRCxLQUFLLENBQUMsNEJBQTRCTDtZQUM5QztRQUNKO1FBQ0FJLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLHVDQUF1Q0E7UUFDekQ7SUFDSjtJQUNBLE1BQU0sQ0FDRjhFLCtCQUNBLEVBQUViLFNBQVNjLG9DQUFvQyxFQUFFLENBQ3BELEdBQUc5Syw0REFBV0EsQ0FBQ2IsOEVBQXVCQSxFQUFFO1FBQ3JDc0csYUFBYSxDQUFDQztZQUNWLE1BQU1DLE9BQU9ELFNBQVNxRixxQkFBcUI7WUFDM0MsSUFBSXBGLEtBQUtZLEVBQUUsR0FBRyxHQUFHO2dCQUNiLHNDQUFzQztnQkFDdEMsSUFBSS9CLHFCQUFxQjJGLE1BQU0sR0FBRyxHQUFHO29CQUNqQyxNQUFNM0Isa0JBQWtCaEUscUJBQXFCeUMsR0FBRyxDQUM1QyxDQUFDbUQ7NEJBRU96SCxvQ0FBQUEsOEJBWVMrQjt3QkFiYixNQUFNc0csZ0JBQ0ZySSx3QkFBQUEsbUNBQUFBLCtCQUFBQSxZQUFhNkYsZUFBZSxjQUE1QjdGLG9EQUFBQSxxQ0FBQUEsNkJBQThCa0QsS0FBSyxjQUFuQ2xELHlEQUFBQSxtQ0FBcUMwSCxJQUFJLENBQ3JDLENBQUMzQixRQUNHQSxNQUFNRSwwQkFBMEIsS0FDaEN3QixlQUFlekIsT0FBTzt3QkFHbEMsT0FBTzs0QkFDSHBDLEVBQUUsRUFBRXlFLDBCQUFBQSxvQ0FBQUEsY0FBZXpFLEVBQUU7NEJBQ3JCc0MsUUFBUXVCLGVBQWV2QixNQUFNLEdBQUcsT0FBTzs0QkFDdkNJLG1CQUFtQnRELEtBQUtZLEVBQUU7NEJBQzFCcUMsNEJBQ0l3QixlQUFlekIsT0FBTzs0QkFDMUJJLE9BQU8sR0FBRXJFLDJCQUFBQSxtQkFBbUIyRixJQUFJLENBQzVCLENBQUN0QixVQUNHQSxRQUFRSixPQUFPLElBQ2Z5QixlQUFlekIsT0FBTyxlQUhyQmpFLCtDQUFBQSx5QkFJTnFFLE9BQU87d0JBQ2Q7b0JBQ0o7b0JBRUpQLGdCQUFnQjhCLE9BQU8sQ0FBQyxDQUFDRjt3QkFDckIsSUFBSUEsZUFBZTdELEVBQUUsRUFBRTs0QkFDbkIsd0JBQXdCOzRCQUN4QjBFLG1DQUFtQztnQ0FDL0JqQyxXQUFXO29DQUNQd0IsT0FBT0o7Z0NBQ1g7NEJBQ0o7d0JBQ0osT0FBTzs0QkFDSCxtQkFBbUI7NEJBQ25CLE1BQU0sRUFBRTdELEVBQUUsRUFBRSxHQUFHMkUsYUFBYSxHQUFHZDs0QkFDL0JHLG1DQUFtQztnQ0FDL0J2QixXQUFXO29DQUNQd0IsT0FBT1U7Z0NBQ1g7NEJBQ0o7d0JBQ0o7b0JBQ0o7Z0JBQ0o7Z0JBQ0FUO2dCQUNBQyxpQkFBaUJuSTtnQkFDakJvSSxtQkFBbUJoRixLQUFLMEIsZUFBZTtnQkFDdkMsSUFBSSxDQUFDN0UsV0FBVyxHQUFHO29CQUNmSyxPQUFPK0gsSUFBSSxDQUFDLGlCQUEwQixPQUFUcEk7Z0JBQ2pDLE9BQU8sSUFBSSxDQUFDRSxXQUFXLEdBQUc7b0JBQ3RCRyxPQUFPK0gsSUFBSSxDQUFDLG1CQUE0QixPQUFUbEk7Z0JBQ25DLE9BQU87b0JBQ0hHLE9BQU8rSCxJQUFJLENBQUM7Z0JBQ2hCO1lBQ0osT0FBTztnQkFDSDVFLFFBQVFELEtBQUssQ0FBQyx1Q0FBdUNMO1lBQ3pEO1FBQ0o7UUFDQUksU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsdUNBQXVDQTtRQUN6RDtJQUNKO0lBQ0EsTUFBTSxDQUNGb0YsMkJBQ0EsRUFBRW5CLFNBQVNvQixnQ0FBZ0MsRUFBRSxDQUNoRCxHQUFHbkwsNkRBQVlBLENBQUNMLGlGQUE2QkEsRUFBRTtRQUM1QzRGLGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWLE9BQU9BLFNBQVN5Rix5QkFBeUIsQ0FBQ3hGLElBQUk7UUFDbEQ7UUFDQUcsU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsMkNBQTJDQTtZQUN6RCxPQUFPO1FBQ1g7SUFDSjtJQUNBLE1BQU1zRixxQ0FBcUM7WUFDdkNyQyw2RUFBaUIsQ0FBQyxHQUNsQnZEO1FBRUEsTUFBTSxFQUFFRSxJQUFJLEVBQUUsR0FBUSxNQUFNd0YsMEJBQTBCO1lBQ2xEbkMsV0FBV0E7UUFDZjtRQUNBdkQsWUFBWUUsS0FBS3dGLHlCQUF5QjtJQUM5QztJQUVBLE1BQU0sQ0FDRkcsa0NBQ0EsRUFBRXRCLFNBQVN1QiwrQkFBK0IsRUFBRSxDQUMvQyxHQUFHdkwsNERBQVdBLENBQUNWLGtGQUEyQkEsRUFBRTtRQUN6Q21HLGFBQWEsQ0FBQ0MsWUFBbUI7UUFDakNJLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDcEQ7SUFDSjtJQUNBLE1BQU0sQ0FDRnlGLGtDQUNBLEVBQUV4QixTQUFTeUIsK0JBQStCLEVBQUUsQ0FDL0MsR0FBR3pMLDREQUFXQSxDQUFDVCxrRkFBMkJBLEVBQUU7UUFDekNrRyxhQUFhLENBQUNDLFlBQW1CO1FBQ2pDSSxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO1FBQ3BEO0lBQ0o7SUFDQSxNQUFNMEUsNEJBQTRCO1FBQzlCLE1BQU1pQixzQkFBMkIsRUFBRTtRQUNuQyxNQUFNdkcsV0FBV2xDLFNBQVNpQyxRQUFRO1FBQ2xDakMsU0FBUytCLGFBQWEsQ0FBQ3NGLE9BQU8sQ0FBQyxDQUFDaEQ7WUFDNUIsTUFBTXFFLGVBQWU3SCxjQUFjdUcsSUFBSSxDQUFDLENBQUN1QixLQUFZQSxHQUFHckYsRUFBRSxLQUFLZTtZQUUvRCxJQUFJLENBQUM1SSxxREFBT0EsQ0FBQ2lOLGlCQUFpQkEsYUFBYUUsV0FBVyxHQUFHLEdBQUc7Z0JBQ3hELE1BQU1DLGlCQUFpQnhFO2dCQUN2QixNQUFNeUUsYUFBYXROLDRDQUFLQSxDQUFDd0UsU0FBU0ssSUFBSSxFQUFFMEksR0FBRyxDQUN2Q0wsYUFBYUUsV0FBVyxFQUN4QjtnQkFFSjVJLFNBQVM4RCxPQUFPLENBQUN1RCxPQUFPLENBQUMsQ0FBQ3BEO29CQUN0QixNQUFNK0UsV0FBVy9FO29CQUNqQndFLG9CQUFvQmQsSUFBSSxDQUFDO3dCQUNyQnNCLFNBQVNILFdBQVdqRixNQUFNLENBQUM7d0JBQzNCbUYsVUFBVUE7d0JBQ1Y5RyxVQUFVQTt3QkFDVjJHLGdCQUFnQkE7b0JBQ3BCO2dCQUNKO1lBQ0o7UUFDSjtRQUNBLElBQUlLLDRCQUFpQyxFQUFFO1FBQ3ZDLElBQUksQ0FBQ3pOLHFEQUFPQSxDQUFDZ04sc0JBQXNCO1lBQy9CLE1BQU1VLFFBQVFDLEdBQUcsQ0FDYlgsb0JBQW9CekUsR0FBRyxDQUFDLE9BQU9xRjtnQkFDM0IsTUFBTXRELFlBQVk7b0JBQ2RoQixRQUFRO3dCQUNKaUUsVUFBVTs0QkFDTi9DLElBQUlvRCxLQUFLTCxRQUFRO3dCQUNyQjt3QkFDQTlHLFVBQVU7NEJBQ04rRCxJQUFJb0QsS0FBS25ILFFBQVE7d0JBQ3JCO3dCQUNBMkcsZ0JBQWdCOzRCQUNaNUMsSUFBSW9ELEtBQUtSLGNBQWM7d0JBQzNCO29CQUNKO2dCQUNKO2dCQUNBLE1BQU1yRyxjQUFjLENBQUNDO3dCQUdUQTtvQkFGUnlHLDBCQUEwQnZCLElBQUksQ0FBQzt3QkFDM0IsR0FBRzBCLElBQUk7d0JBQ1AvRixJQUFJYixDQUFBQSxlQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVhLEVBQUUsY0FBWmIsMEJBQUFBLGVBQWdCO29CQUN4QjtnQkFDSjtnQkFFQSxNQUFNMkYsbUNBQ0ZyQyxXQUNBdkQ7WUFFUjtRQUVSO1FBRUEsSUFBSSxDQUFDL0cscURBQU9BLENBQUN5Tiw0QkFBNEI7WUFDckMsTUFBTUMsUUFBUUMsR0FBRyxDQUNiRSxNQUFNQyxJQUFJLENBQUNMLDJCQUEyQmxGLEdBQUcsQ0FBQyxPQUFPcUY7Z0JBQzdDLE1BQU10RCxZQUFZO29CQUNkQSxXQUFXO3dCQUFFd0IsT0FBTzhCO29CQUFLO2dCQUM3QjtnQkFDQSxJQUFJQSxLQUFLL0YsRUFBRSxLQUFLLEdBQUc7b0JBQ2YsTUFBTStFLGlDQUFpQ3RDO2dCQUMzQyxPQUFPO29CQUNILE1BQU13QyxpQ0FBaUN4QztnQkFDM0M7WUFDSjtRQUVSO0lBQ0o7SUFFQSxNQUFNLENBQUN1QixtQ0FBbUMsR0FBR3ZLLDREQUFXQSxDQUNwRFIsNkZBQXNDQSxFQUN0QztRQUNJaUcsYUFBYSxDQUFDQztnQkFFUy9DO1lBRG5CLE1BQU1nRCxPQUFPRCxTQUFTNkUsa0NBQWtDO1lBQ3hELElBQUk1RSxLQUFLWSxFQUFFLEdBQUcsTUFBSzVELHdCQUFBQSxtQ0FBQUEsK0JBQUFBLFlBQWE2RixlQUFlLGNBQTVCN0YsbURBQUFBLDZCQUE4QmtELEtBQUssR0FBRTtnQkFDcEQxQyxlQUFlO29CQUNYLEdBQUdSLFdBQVc7b0JBQ2Q2RixpQkFBaUI7d0JBQ2IsR0FBRzdGLFlBQVk2RixlQUFlO3dCQUM5QjNDLE9BQU87K0JBQUlsRCxZQUFZNkYsZUFBZSxDQUFDM0MsS0FBSzs0QkFBRUY7eUJBQUs7b0JBQ3ZEO2dCQUNKO1lBQ0osT0FBTztnQkFDSEssUUFBUUQsS0FBSyxDQUNULDRDQUNBTDtZQUVSO1FBQ0o7UUFDQUksU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsNENBQTRDQTtRQUM5RDtJQUNKO0lBR0osTUFBTSxDQUFDa0YsbUNBQW1DLEdBQUdqTCw0REFBV0EsQ0FDcERQLDZGQUFzQ0EsRUFDdEM7UUFDSWdHLGFBQWEsQ0FBQ0M7WUFDVixNQUFNQyxPQUFPRCxTQUFTdUYsa0NBQWtDO1lBQ3hELElBQUl0RixLQUFLWSxFQUFFLEdBQUcsR0FBRztvQkFNRTVEO2dCQUxmUSxlQUFlO29CQUNYLEdBQUdSLFdBQVc7b0JBQ2Q2RixpQkFBaUI7d0JBQ2IsR0FBRzdGLFlBQVk2RixlQUFlO3dCQUM5QjNDLE9BQU87K0JBQ0FsRCx3QkFBQUEsbUNBQUFBLCtCQUFBQSxZQUFhNkYsZUFBZSxjQUE1QjdGLG1EQUFBQSw2QkFBOEJrRCxLQUFLLENBQUNtQyxNQUFNLENBQ3pDLENBQUNvQyxpQkFDR0EsZUFBZXhCLDBCQUEwQixLQUN6Q2pELEtBQUtpRCwwQkFBMEI7NEJBRXZDO2dDQUNJLEdBQUdqRCxJQUFJOzRCQUNYO3lCQUNIO29CQUNMO2dCQUNKO1lBQ0osT0FBTztnQkFDSEssUUFBUUQsS0FBSyxDQUNULDRDQUNBTDtZQUVSO1FBQ0o7UUFDQUksU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsNENBQTRDQTtRQUM5RDtJQUNKO0lBR0osTUFBTTBHLGFBQWE1TixrREFBV0EsQ0FBQztZQW9EZG9FLG1CQUdNQTtRQXREbkIsSUFBSXlKLFlBQVk7UUFDaEIsSUFBSUMsU0FBUztZQUNUM0gsZUFBZTtZQUNmQyxXQUFXO1lBQ1hDLFVBQVU7WUFDVjVCLE1BQU07UUFDVjtRQUNBeUIsY0FBYzRIO1FBQ2QsSUFBSWpPLHFEQUFPQSxDQUFDdUUsU0FBUytCLGFBQWEsR0FBRztZQUNqQzBILFlBQVk7WUFDWkMsT0FBTzNILGFBQWEsR0FBRztRQUMzQjtRQUNBLElBQUksQ0FBRS9CLENBQUFBLFNBQVNnQyxTQUFTLElBQUloQyxTQUFTZ0MsU0FBUyxHQUFHLElBQUk7WUFDakR5SCxZQUFZO1lBQ1pDLE9BQU8xSCxTQUFTLEdBQUc7UUFDdkI7UUFDQSxJQUNJLENBQUNoQyxTQUFTaUMsUUFBUSxJQUNsQixDQUFFakMsQ0FBQUEsU0FBUzJKLGtCQUFrQixJQUFJM0osU0FBUzJKLGtCQUFrQixJQUFJLElBQ2xFO1lBQ0VGLFlBQVk7WUFDWkMsT0FBT3pILFFBQVEsR0FBRztRQUN0QjtRQUVBLElBQUksT0FBT2pDLFNBQVNLLElBQUksS0FBSyxhQUFhO1lBQ3RDTCxTQUFTSyxJQUFJLEdBQUc3RSw0Q0FBS0EsR0FBR3FJLE1BQU0sQ0FBQztRQUNuQztRQUVBLElBQUk3RCxTQUFTSyxJQUFJLEtBQUssUUFBUSxDQUFDN0UsNENBQUtBLENBQUN3RSxTQUFTSyxJQUFJLEVBQUV1SixPQUFPLElBQUk7WUFDM0RILFlBQVk7WUFDWkMsT0FBT3JKLElBQUksR0FBRztRQUNsQjtRQUNBLElBQUlvSixXQUFXO1lBQ1hsSixpQkFBaUI7WUFDakJ1QixjQUFjNEg7WUFDZDdKLE1BQU07Z0JBQ0YwRyxPQUFPO2dCQUNQc0QsYUFDSUgsT0FBTzNILGFBQWEsSUFDcEIySCxPQUFPMUgsU0FBUyxJQUNoQjBILE9BQU96SCxRQUFRLElBQ2Z5SCxPQUFPckosSUFBSTtnQkFDZnlKLFNBQVM7WUFDYjtZQUNBO1FBQ0o7UUFDQSxNQUFNdkMsUUFBUTtZQUNWakUsSUFBSWhFO1lBQ0pvRSxNQUFNMUQsU0FBU0ssSUFBSSxHQUNiN0UsNENBQUtBLENBQUN3RSxTQUFTSyxJQUFJLEVBQUV3RCxNQUFNLENBQUMsZ0JBQzVCO1lBQ05FLE9BQU8sR0FBRS9ELG9CQUFBQSxTQUFTOEQsT0FBTyxjQUFoQjlELHdDQUFBQSxrQkFBa0IrSixJQUFJLENBQUM7WUFDaENDLFdBQVdoSyxTQUFTZ0MsU0FBUztZQUM3Qm9DLGlCQUFpQnJEO1lBQ2pCRixhQUFhLEdBQUViLDBCQUFBQSxTQUFTK0IsYUFBYSxjQUF0Qi9CLDhDQUFBQSx3QkFBd0IrSixJQUFJLENBQUM7WUFDNUM3SCxRQUFRLEVBQUVsQyxxQkFBQUEsK0JBQUFBLFNBQVVpQyxRQUFRO1lBQzVCZ0ksc0JBQXNCakssQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVaUMsUUFBUSxJQUNsQ2pDLFNBQVNpQyxRQUFRLEtBQUssV0FDdEJqQyxTQUFTaUMsUUFBUSxLQUFLLFlBQ2xCakMsU0FBU2lDLFFBQVEsR0FDakIsV0FDSjtRQUNWO1FBQ0EsSUFBSTNDLGVBQWUsR0FBRztZQUNsQixNQUFNd0gsOEJBQThCO2dCQUNoQ2YsV0FBVztvQkFDUHdCLE9BQU9BO2dCQUNYO1lBQ0o7UUFDSixPQUFPO1lBQ0gsTUFBTUssOEJBQThCO2dCQUNoQzdCLFdBQVc7b0JBQ1B3QixPQUFPQTtnQkFDWDtZQUNKO1FBQ0o7SUFDSixHQUFHO1FBQ0N2SDtRQUNBVjtRQUNBeUI7UUFDQVE7UUFDQUU7UUFDQS9CO1FBQ0FIO1FBQ0FFO1FBQ0FxSDtRQUNBYztRQUNBaEk7UUFDQUM7S0FDSDtJQUNELHlCQUF5QjtJQUV6QixNQUFNNEgsbUJBQW1CLENBQUN5QztRQUN0QnhKLGlCQUFpQndHLE1BQU0sR0FBRyxNQUN0QnhHLDZCQUFBQSx1Q0FBQUEsaUJBQWtCMkcsT0FBTyxDQUFDLENBQUM4QztZQUN2QkMsc0JBQXNCRCxXQUFXRDtRQUNyQztJQUNSO0lBRUEsTUFBTUUsd0JBQXdCLE9BQzFCRCxXQUNBRDtRQUVBLE1BQU1HLGlDQUFpQztZQUNuQ3RFLFdBQVc7Z0JBQ1BoQixRQUFRO29CQUNKaUUsVUFBVTt3QkFBRS9DLElBQUlrRSxVQUFVaEYsUUFBUTtvQkFBQztvQkFDbkNhLG1CQUFtQjt3QkFBRXNFLElBQUlKO29CQUFXO2dCQUN4QztZQUNKO1FBQ0osR0FDS0ssSUFBSSxDQUFDLENBQUM5SDtZQUNILE1BQU1DLE9BQU9ELFNBQVNDLElBQUksQ0FBQzhILDZCQUE2QixDQUFDNUgsS0FBSztZQUM5RCxJQUFJRixLQUFLd0UsTUFBTSxHQUFHLEdBQUc7Z0JBQ2pCdUQsc0NBQXNDO29CQUNsQzFFLFdBQVc7d0JBQ1B3QixPQUFPOzRCQUNIakUsSUFBSVosSUFBSSxDQUFDLEVBQUUsQ0FBQ1ksRUFBRTs0QkFDZDBGLFVBQVVtQixVQUFVaEYsUUFBUTs0QkFDNUJHLGVBQWU2RSxVQUFVOUUsYUFBYTs0QkFDdENXLG1CQUFtQmtFO3dCQUN2QjtvQkFDSjtnQkFDSjtZQUNKLE9BQU87Z0JBQ0gsSUFBSUMsVUFBVTlFLGFBQWEsRUFBRTtvQkFDekJxRixzQ0FBc0M7d0JBQ2xDM0UsV0FBVzs0QkFDUHdCLE9BQU87Z0NBQ0h5QixVQUFVbUIsVUFBVWhGLFFBQVE7Z0NBQzVCRyxlQUFlNkUsVUFBVTlFLGFBQWE7Z0NBQ3RDVyxtQkFBbUJrRTs0QkFDdkI7d0JBQ0o7b0JBQ0o7Z0JBQ0o7WUFDSjtRQUNKLEdBQ0NTLEtBQUssQ0FBQyxDQUFDN0g7WUFDSkMsUUFBUUQsS0FBSyxDQUNULDZDQUNBQTtRQUVSO0lBQ1I7SUFFQSxNQUFNLENBQUN1SCxpQ0FBaUMsR0FBR3JOLDZEQUFZQSxDQUNuRFAsa0ZBQThCQTtJQUdsQyxNQUFNLENBQ0ZnTyx1Q0FDQSxFQUFFMUQsU0FBUzZELDRDQUE0QyxFQUFFLENBQzVELEdBQUc3Tiw0REFBV0EsQ0FBQ1gsdUZBQWdDQSxFQUFFO1FBQzlDb0csYUFBYSxDQUFDQztZQUNWLE1BQU1DLE9BQU9ELFNBQVNvSSw4QkFBOEI7WUFDcEQsSUFBSW5JLEtBQUtZLEVBQUUsR0FBRyxHQUFHO1lBQ2IsbUJBQW1CO1lBQ25CLG9EQUFvRDtZQUNwRCxJQUFJO1lBQ1IsT0FBTztnQkFDSFAsUUFBUUQsS0FBSyxDQUNULCtDQUNBTDtZQUVSO1FBQ0o7UUFDQUksU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsK0NBQStDQTtRQUNqRTtJQUNKO0lBRUEsTUFBTSxDQUNGNEgsdUNBQ0EsRUFBRTNELFNBQVMrRCw0Q0FBNEMsRUFBRSxDQUM1RCxHQUFHL04sNERBQVdBLENBQUNaLHVGQUFnQ0EsRUFBRTtRQUM5Q3FHLGFBQWEsQ0FBQ0M7WUFDVixNQUFNQyxPQUFPRCxTQUFTc0ksOEJBQThCO1lBQ3BELElBQUlySSxLQUFLWSxFQUFFLEdBQUcsR0FBRztZQUNiLG1CQUFtQjtZQUNuQixvREFBb0Q7WUFDcEQsSUFBSTtZQUNSLE9BQU87Z0JBQ0hQLFFBQVFELEtBQUssQ0FDVCwrQ0FDQUw7WUFFUjtRQUNKO1FBQ0FJLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLCtDQUErQ0E7UUFDakU7SUFDSjtJQUVBLE1BQU1rSSwyQkFBMkJwUCxrREFBV0EsQ0FBQyxDQUFDOEg7UUFDMUN0RCxnQkFBZ0JzRCxRQUFRLElBQUlyRCxLQUFLcUQsS0FBS3VILFFBQVE7UUFDOUNoTCxZQUFZLENBQUNvRCxlQUF1QjtnQkFDaEMsR0FBR0EsWUFBWTtnQkFDZmhELE1BQU03RSw0Q0FBS0EsQ0FBQ2tJLE1BQU1HLE1BQU0sQ0FBQztZQUM3QjtJQUNKLEdBQUcsRUFBRTtJQUVMLE1BQU1xSCxzQkFBc0J0UCxrREFBV0EsQ0FBQyxDQUFDc0k7UUFDckMsSUFBSSxDQUFDQSxTQUFTLFFBQU8sc0NBQXNDO1FBRTNELHFGQUFxRjtRQUNyRmpFLFlBQVksQ0FBQ29EO1lBQ1QsTUFBTThILGFBQWEsSUFBSUMsSUFBSS9ILENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY1MsT0FBTyxLQUFJLEVBQUU7WUFDdERxSCxXQUFXcEMsR0FBRyxDQUFDN0UsUUFBUU8sS0FBSztZQUM1QixNQUFNVixVQUFVdUYsTUFBTUMsSUFBSSxDQUFDNEI7WUFDM0IsT0FBTztnQkFDSCxHQUFHOUgsWUFBWTtnQkFDZnJCLFdBQVdrQyxRQUFRTyxLQUFLO2dCQUN4QlgsU0FBU0M7WUFDYjtRQUNKO1FBQ0F0RCxzQkFBc0IsQ0FBQzRLLFdBQWtCO21CQUFJQTtnQkFBVW5IO2FBQVE7UUFDL0R2RCxvQkFBb0IsQ0FBQzJLLGlCQUF3QjttQkFDdENBO2dCQUNIO29CQUNJbkcsVUFBVSxDQUFDakIsUUFBUU8sS0FBSztvQkFDeEJZLGVBQWU7Z0JBQ25CO2FBQ0g7SUFDTCxHQUFHLEVBQUU7SUFFTCxNQUFNa0csMkJBQTJCM1Asa0RBQVdBLENBQUMsQ0FBQ2lGO1FBQzFDWixZQUFZLENBQUNvRCxlQUF1QjtnQkFDaEMsR0FBR0EsWUFBWTtnQkFDZnRCLGVBQWVsQixjQUFjbUQsR0FBRyxDQUFDLENBQUNxRixPQUFjQSxLQUFLNUUsS0FBSztZQUM5RDtJQUNKLEdBQUcsRUFBRTtJQUNMOzs7Ozs7TUFNRSxHQUNGLE1BQU0rRyxxQkFBcUI1UCxrREFBV0EsQ0FBQyxDQUFDbUk7UUFDcENoQixRQUFRMEksR0FBRyxDQUFDLCtEQUFxRDtZQUM3RDFIO1lBQ0EySCxhQUFhLEVBQUUzSCxvQkFBQUEsOEJBQUFBLFFBQVNtRCxNQUFNO1lBQzlCeUUsYUFBYSxPQUFPNUg7UUFDeEI7UUFFQSxNQUFNNkgsWUFBWTdILFFBQVFDLEdBQUcsQ0FBQyxDQUFDcUYsT0FBY0EsS0FBSzVFLEtBQUs7UUFDdkQxQixRQUFRMEksR0FBRyxDQUFDLHFEQUEyQ0c7UUFFdkQzTCxZQUFZLENBQUNvRCxlQUF1QjtnQkFDaEMsR0FBR0EsWUFBWTtnQkFDZlMsU0FBUzhIO1lBQ2I7UUFDQW5MLHNCQUFzQnNEO1FBQ3RCcEQsb0JBQW9CLENBQUMySyxpQkFDakJBLGVBQWV2RyxNQUFNLENBQUMsQ0FBQ3NFLE9BQ25CdEYsUUFBUThILElBQUksQ0FBQyxDQUFDNUgsSUFBVyxDQUFDQSxFQUFFUSxLQUFLLEtBQUs0RSxLQUFLbEUsUUFBUTtJQUcvRCxHQUFHLEVBQUU7SUFDTCxNQUFNMkcscUJBQXFCLENBQ3ZCQyxHQUNBM0csUUFDQTdGO1FBRUEsTUFBTXlNLFFBQVF0TCxpQkFBaUJ1TCxTQUFTLENBQ3BDLENBQUNDLFNBQVdBLE9BQU8vRyxRQUFRLEtBQUs1RjtRQUVwQyxNQUFNNE0saUJBQWlCO2VBQUl6TDtTQUFpQjtRQUM1QyxJQUFJcUwsR0FBRztZQUNILElBQUlDLFVBQVUsQ0FBQyxHQUFHO2dCQUNkLElBQUlELEVBQUVLLElBQUksT0FBTyxJQUFJO29CQUNqQkQsZUFBZUUsTUFBTSxDQUFDTCxPQUFPO2dCQUNqQyxPQUFPO29CQUNIRyxjQUFjLENBQUNILE1BQU0sQ0FBQzNHLGFBQWEsR0FBRzBHO2dCQUMxQztZQUNKLE9BQU87Z0JBQ0hJLGVBQWV4RSxJQUFJLENBQUM7b0JBQUV4QyxVQUFVNUY7b0JBQVU4RixlQUFlMEc7Z0JBQUU7WUFDL0Q7UUFDSixPQUFPO1lBQ0hJLGVBQWVFLE1BQU0sQ0FBQ0wsT0FBTztRQUNqQztRQUNBckwsb0JBQW9Cd0w7SUFDeEI7SUFFQSxNQUFNRyw2QkFBNkIxUSxrREFBV0EsQ0FDMUMsQ0FBQytJO1FBQ0cxRSxZQUFZLENBQUNvRCxlQUF1QjtnQkFDaEMsR0FBR0EsWUFBWTtnQkFDZnBCLFVBQVUwQyxTQUNKLE9BQU9BLFdBQVcsWUFBWSxDQUFDMkUsTUFBTWlELE9BQU8sQ0FBQzVILFVBQ3pDQSxPQUFPRixLQUFLLEdBQ1osSUFDSjtZQUNWO0lBQ0osR0FDQSxFQUFFO0lBR04sTUFBTWlELHFCQUFxQjlMLGtEQUFXQSxDQUFDLENBQUM0UTtRQUNwQ3hMLFdBQVd3TDtJQUNmLEdBQUcsRUFBRTtJQUVMOVEsZ0RBQVNBLENBQUM7UUFDTixJQUFJLENBQUNELHFEQUFPQSxDQUFDdUUsV0FBVztZQUNwQixNQUFNeU0sTUFDRmhOLFdBQVcsS0FBS2lOLE1BQU1DLFNBQVMzTSxxQkFBQUEsK0JBQUFBLFNBQVVpQyxRQUFRLEVBQUUsT0FDN0N4QyxXQUNBa04sU0FBUzNNLHFCQUFBQSwrQkFBQUEsU0FBVWlDLFFBQVEsRUFBRTtZQUN2Q0UsWUFBWXNLO1FBQ2hCO0lBQ0osR0FBRztRQUFDaE47UUFBVU87S0FBUztJQUV2QixNQUFNLENBQUM0TSxhQUFhQyxlQUFlLEdBQUdsUiwrQ0FBUUEsQ0FBTTtJQUVwREQsZ0RBQVNBLENBQUM7UUFDTm1SLGVBQWUxUCxvRUFBY0E7SUFDakMsR0FBRyxFQUFFO0lBRUwsSUFDSSxDQUFDeVAsZUFDQSxDQUFDeFAsdUVBQWFBLENBQUMsaUJBQWlCd1AsZ0JBQzdCLENBQUN4UCx1RUFBYUEsQ0FBQyxpQkFBaUJ3UCxnQkFDaEMsQ0FBQ3hQLHVFQUFhQSxDQUFDLG1CQUFtQndQLGNBQ3hDO1FBQ0UsT0FBTyxDQUFDQSw0QkFDSiw4REFBQ3ZQLHFEQUFPQTs7OztzQ0FFUiw4REFBQ0EscURBQU9BO1lBQUN5UCxjQUFhOzs7Ozs7SUFFOUI7SUFFQSxNQUFNQyxnQkFBZ0I7UUFDbEIsTUFBTUMsYUFBYW5NLGNBQWNrRSxNQUFNLENBQUMsQ0FBQ2tJO2dCQUNyQ2pOO21CQUFBQSxxQkFBQUEsZ0NBQUFBLDBCQUFBQSxTQUFVK0IsYUFBYSxjQUF2Qi9CLDhDQUFBQSx3QkFBeUJnRixRQUFRLENBQUNpSSxLQUFLM0osRUFBRTs7UUFFN0MsT0FBTzBKLFdBQ0ZoSixHQUFHLENBQUMsQ0FBQ2lKO2dCQUtnQjtZQUpsQixPQUFPQSxLQUFLQyx3QkFBd0IsQ0FBQ3RLLEtBQUssQ0FBQ3NFLE1BQU0sR0FBRyxJQUM5QztnQkFDSTVELElBQUkySixLQUFLM0osRUFBRTtnQkFDWGlELE9BQU8wRyxLQUFLMUcsS0FBSztnQkFDakI0RyxNQUFNLEdBQUU7dUJBQ0RGLEtBQUtDLHdCQUF3QixDQUFDdEssS0FBSztpQkFDekMsY0FGTyxrQ0FFTHdLLElBQUksQ0FDSCxDQUFDQyxHQUFRQyxJQUFXRCxFQUFFRSxTQUFTLEdBQUdELEVBQUVDLFNBQVM7WUFFckQsSUFDQTtRQUNWLEdBQ0N4SSxNQUFNLENBQUMsQ0FBQ2tJLE9BQWNBLFFBQVE7SUFDdkM7SUFFQSxNQUFNTyxZQUFZQyxRQUFRO1FBQ3RCLE9BQU9sTyxXQUFXLElBQUk7WUFBQ0EsU0FBUzBMLFFBQVE7U0FBRyxHQUFHakwscUJBQUFBLCtCQUFBQSxTQUFVOEQsT0FBTztJQUNuRSxHQUFHO1FBQUN2RTtRQUFVUyxxQkFBQUEsK0JBQUFBLFNBQVU4RCxPQUFPO0tBQUM7SUFFaEMsTUFBTTRKLHdCQUF3QkQsUUFBUTtRQUNsQyxPQUNJek4sWUFDQWEsY0FBY2tFLE1BQU0sQ0FDaEIsQ0FBQ2tJO2dCQUNHak47bUJBQUFBLENBQUFBLHFCQUFBQSxnQ0FBQUEsMEJBQUFBLFNBQVUrQixhQUFhLGNBQXZCL0IsOENBQUFBLHdCQUF5QmdGLFFBQVEsQ0FBQ2lJLEtBQUszSixFQUFFLE1BQ3pDMkosS0FBS1UsU0FBUztXQUNwQnpHLE1BQU0sR0FBRztJQUVuQixHQUFHO1FBQUNsSDtRQUFVYTtLQUFjO0lBRTVCLE1BQU0rTSx3QkFBd0IsQ0FBQ25JLE9BQVl3SCxNQUFXckg7UUFDbEQseUVBQXlFO1FBQ3pFLE1BQU1pSSxpQkFBaUJ0TSxxQkFBcUJ3RCxNQUFNLENBQzlDLENBQUNvQyxpQkFBd0JBLGVBQWV6QixPQUFPLEtBQUtELE1BQU1uQyxFQUFFO1FBRWhFOUIsd0JBQXdCO2VBQ2pCcU07WUFDSDtnQkFBRW5JLFNBQVNELE1BQU1uQyxFQUFFO2dCQUFFc0MsUUFBUUE7WUFBTztTQUN2QztJQUNMO0lBRUEsTUFBTWtJLGlCQUFpQixDQUFDckk7WUFTQS9GLG9DQUFBQTtRQVJwQixJQUFJNkIscUJBQXFCMkYsTUFBTSxHQUFHLEdBQUc7WUFDakMsTUFBTTZHLGNBQWN4TSxxQkFBcUI2RixJQUFJLENBQ3pDLENBQUNELGlCQUF3QkEsZUFBZXpCLE9BQU8sSUFBSUQsTUFBTW5DLEVBQUU7WUFFL0QsSUFBSXlLLGFBQWE7Z0JBQ2IsT0FBT0EsWUFBWW5JLE1BQU0sR0FBRyxPQUFPO1lBQ3ZDO1FBQ0o7UUFDQSxNQUFNbUksY0FBY3JPLHdCQUFBQSxtQ0FBQUEsK0JBQUFBLFlBQWE2RixlQUFlLGNBQTVCN0Ysb0RBQUFBLHFDQUFBQSw2QkFBOEJrRCxLQUFLLGNBQW5DbEQseURBQUFBLG1DQUFxQzBILElBQUksQ0FDekQsQ0FBQ0QsaUJBQ0dBLGVBQWV4QiwwQkFBMEIsSUFBSUYsTUFBTW5DLEVBQUU7UUFFN0QsT0FBT3lLLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYW5JLE1BQU0sS0FBSTtJQUNsQztJQUVBLE1BQU1vSSxtQkFBbUIsQ0FBQ3ZJO1lBQ0QvRixvQ0FBQUE7UUFBckIsTUFBTXVPLGVBQWV2Tyx3QkFBQUEsbUNBQUFBLCtCQUFBQSxZQUFhNkYsZUFBZSxjQUE1QjdGLG9EQUFBQSxxQ0FBQUEsNkJBQThCa0QsS0FBSyxjQUFuQ2xELHlEQUFBQSxtQ0FBcUMwSCxJQUFJLENBQzFELENBQUNELGlCQUNHQSxlQUFleEIsMEJBQTBCLElBQUlGLE1BQU1uQyxFQUFFO1FBRTdELElBQUk3QixtQkFBbUJ5RixNQUFNLEdBQUcsR0FBRztZQUMvQixNQUFNK0csZUFBZXhNLG1CQUFtQjJGLElBQUksQ0FDeEMsQ0FBQ0QsaUJBQXdCQSxlQUFlekIsT0FBTyxJQUFJRCxNQUFNbkMsRUFBRTtZQUUvRGxDLGtCQUFrQjZNLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY25JLE9BQU8sS0FBSTtRQUMvQyxPQUFPO1lBQ0gxRSxrQkFBa0I2TSxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWNuSSxPQUFPLEtBQUk7UUFDL0M7UUFDQXhFLGdCQUFnQm1FO1FBQ2hCN0Qsb0JBQW9CO0lBQ3hCO0lBRUEsTUFBTXNNLGFBQWEsQ0FBQ3pJO1lBU0svRixvQ0FBQUE7UUFSckIsSUFBSStCLG1CQUFtQnlGLE1BQU0sR0FBRyxHQUFHO1lBQy9CLE1BQU0rRyxlQUFleE0sbUJBQW1CMkYsSUFBSSxDQUN4QyxDQUFDRCxpQkFBd0JBLGVBQWV6QixPQUFPLElBQUlELE1BQU1uQyxFQUFFO1lBRS9ELElBQUkySyxjQUFjO2dCQUNkLE9BQU9BLGFBQWFuSSxPQUFPO1lBQy9CO1FBQ0o7UUFDQSxNQUFNbUksZUFBZXZPLHdCQUFBQSxtQ0FBQUEsK0JBQUFBLFlBQWE2RixlQUFlLGNBQTVCN0Ysb0RBQUFBLHFDQUFBQSw2QkFBOEJrRCxLQUFLLGNBQW5DbEQseURBQUFBLG1DQUFxQzBILElBQUksQ0FDMUQsQ0FBQ0QsaUJBQ0dBLGVBQWV4QiwwQkFBMEIsSUFBSUYsTUFBTW5DLEVBQUU7UUFFN0QsT0FBTzJLLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY25JLE9BQU8sS0FBSUwsTUFBTUssT0FBTztJQUNqRDtJQUVBLE1BQU1xSSxvQkFBb0J2UyxrREFBV0EsQ0FBQztRQUNsQyx5RUFBeUU7UUFDekUsTUFBTXFTLGVBQWV4TSxtQkFBbUJzRCxNQUFNLENBQzFDLENBQUNvQyxpQkFBd0JBLGVBQWV6QixPQUFPLEtBQUtyRSxhQUFhaUMsRUFBRTtRQUV2RTVCLHNCQUFzQjtlQUNmdU07WUFDSDtnQkFBRXZJLFNBQVNyRSxhQUFhaUMsRUFBRTtnQkFBRXdDLFNBQVMzRTtZQUFlO1NBQ3ZEO1FBQ0RTLG9CQUFvQjtJQUN4QixHQUFHO1FBQUNIO1FBQW9CSjtRQUFjRjtLQUFlO0lBRXJELE1BQU1pTix1QkFBdUJ4UyxrREFBV0EsQ0FBQztRQUNyQ3NGLHFCQUFxQjtJQUN6QixHQUFHLEVBQUU7SUFFTCxNQUFNbU4sb0JBQW9CelMsa0RBQVdBLENBQUM7UUFDbENnRSxPQUFPK0gsSUFBSSxDQUFDO0lBQ2hCLEdBQUc7UUFBQy9IO0tBQU87SUFFWCxNQUFNME8sc0JBQXNCMVMsa0RBQVdBLENBQUMsQ0FBQ21RO1FBQ3JDM0ssa0JBQWtCMkssRUFBRXdDLE1BQU0sQ0FBQzlKLEtBQUs7SUFDcEMsR0FBRyxFQUFFO0lBRUwscUJBQ0k7OzBCQUNJLDhEQUFDakgsc0RBQUlBO2dCQUFDZ1IsV0FBVTs7a0NBQ1osOERBQUM5USw0REFBVUE7a0NBQ1AsNEVBQUNDLDJEQUFTQTs7Z0NBQ0wyQixlQUFlLElBQUksUUFBUTtnQ0FBTzs7Ozs7Ozs7Ozs7O2tDQUczQyw4REFBQzFCLGdFQUFTQTt3QkFBQzRRLFdBQVU7Ozs7OztvQkFDcEIsQ0FBQ3hPLFlBQVlWLGFBQWEsa0JBQ3ZCLDhEQUFDeEQsOEVBQTJCQTs7OztrREFFNUIsOERBQUMyQiw2REFBV0E7d0JBQUMrUSxXQUFVOzswQ0FDbkIsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDWCw4REFBQ0M7d0NBQUlELFdBQVU7OzRDQUFpQzswREFFNUMsOERBQUNFO2dEQUFFRixXQUFVOzs7Ozs7NENBQ1pkLHVDQUNHLDhEQUFDelAsMERBQU1BO2dEQUFDMFEsU0FBU1A7MERBQXNCOzs7Ozs7Ozs7Ozs7a0RBSy9DLDhEQUFDSzt3Q0FBSUQsV0FBVTs7MERBQ1gsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDWCw4REFBQ0M7d0RBQUlELFdBQVU7OzBFQUNYLDhEQUFDbFIsd0RBQUtBO2dFQUFDZ0gsT0FBTTswRUFDVCw0RUFBQ3pJLGlHQUFZQTtvRUFDVDRJLEtBQUssRUFBRXpFLHFCQUFBQSwrQkFBQUEsU0FBVWdDLFNBQVM7b0VBQzFCRSxVQUFVQTtvRUFDVjBNLFVBQVUxRDs7Ozs7Ozs7Ozs7MEVBR2xCLDhEQUFDMkQ7Z0VBQU1MLFdBQVU7MEVBQ1psTyxpQkFDR3VCLFdBQVdHLFNBQVM7Ozs7Ozs7Ozs7OztrRUFHaEMsOERBQUN5TTt3REFBSUQsV0FBVTs7MEVBQ1gsOERBQUN6UyxrRUFBK0JBO2dFQUM1QjBJLEtBQUssRUFBRXpFLHFCQUFBQSwrQkFBQUEsU0FBVStCLGFBQWE7Z0VBQzlCNk0sVUFBVXJEOzs7Ozs7MEVBRWQsOERBQUNzRDtnRUFBTUwsV0FBVTswRUFDWmxPLGlCQUNHdUIsV0FBV0UsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUl4Qyw4REFBQzBNO2dEQUFJRCxXQUFVOztrRUFDWCw4REFBQ2xSLHdEQUFLQTtrRUFBQzs7Ozs7O2tFQUNQLDhEQUFDdEIsdUZBQXVCQTt3REFDcEJ5SSxPQUFPK0k7d0RBQ1B0TCxVQUFVQTt3REFDVjBNLFVBQVVwRDs7Ozs7Ozs7Ozs7OzBEQUdsQiw4REFBQzVOLGdFQUFTQTtnREFBQzRRLFdBQVU7Ozs7OzswREFDckIsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDWCw4REFBQ0M7d0RBQUlELFdBQVU7OzBFQUtYLDhEQUFDalEsOERBQVVBO2dFQUNQdVEsTUFBSztnRUFDTEYsVUFBVTVEO2dFQUNWdkcsT0FBTyxJQUFJcEUsS0FBS0Y7Ozs7OzswRUFFcEIsOERBQUMwTztnRUFBTUwsV0FBVTswRUFDWmxPLGlCQUFpQnVCLFdBQVd4QixJQUFJOzs7Ozs7Ozs7Ozs7a0VBR3pDLDhEQUFDb087d0RBQUlELFdBQVU7OzREQUNWN08seUJBQ0csOERBQUNwQyw4REFBUUE7Z0VBQ0x3UixTQUFTcFAsUUFBUXFFLEdBQUcsQ0FDaEIsQ0FBQ1csU0FBaUI7d0VBQ2RMLE9BQU9LLE9BQU9MLEtBQUs7d0VBQ25CRyxPQUFPRSxPQUFPRixLQUFLO29FQUN2QjtnRUFFSnVLLGVBQ0l0UCxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWF1SyxvQkFBb0IsTUFDakMsV0FDTXZLLHdCQUFBQSxtQ0FBQUEsd0JBQUFBLFlBQWF1QyxRQUFRLGNBQXJCdkMsNENBQUFBLHNCQUF1QnVMLFFBQVEsS0FDL0J2TCxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWF1SyxvQkFBb0IsTUFDL0IsWUFDQSxrQkFDQXZLLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYXVLLG9CQUFvQixNQUMvQixVQUNBLFVBQ0F2SyxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWF1SyxvQkFBb0IsTUFDM0IsY0FDSnZLLENBQUFBLHdCQUFBQSxtQ0FBQUEsZ0NBQUFBLFlBQ011UCxnQkFBZ0IsY0FEdEJ2UCxvREFBQUEsOEJBRU00RCxFQUFFLElBQUcsSUFDWDVELHdCQUFBQSxtQ0FBQUEsaUNBQUFBLFlBQWF1UCxnQkFBZ0IsY0FBN0J2UCxxREFBQUEsK0JBQStCNEQsRUFBRSxDQUFDMkgsUUFBUSxLQUMxQ3hMLFdBQ0U7b0VBQ0k2RSxLQUFLLEdBQUUzRSxnQkFBQUEsUUFBUXlILElBQUksQ0FDZixDQUNJekMsU0FFQUEsT0FBT0YsS0FBSyxLQUNaaEYsdUJBTERFLG9DQUFBQSxjQU1KMkUsS0FBSztvRUFDUkcsT0FBT2hGLFNBQVN3TCxRQUFRO2dFQUM1QixJQUNBO2dFQUVsQm5MLFdBQVdKO2dFQUNYa1AsVUFDSXRDO2dFQUVKNEMsYUFBWTs7Ozs7OzBFQUlwQiw4REFBQ0w7Z0VBQU1MLFdBQVU7MEVBQ1psTyxpQkFDR3VCLFdBQVdJLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFhbkMsOERBQUN3TTtnREFBSUQsV0FBVTs7b0RBQ1Z6QixnQkFBZ0I3RixNQUFNLEdBQUcsbUJBQ3RCLDhEQUFDdUg7d0RBQUlELFdBQVU7a0VBQ1Z6QixnQkFBZ0IvSSxHQUFHLENBQ2hCLENBQUNpSixxQkFDRyw4REFBQzdPLHNFQUFVQTs7a0ZBQ1AsOERBQUNkLHdEQUFLQTt3RUFDRmdILE9BQU8ySSxLQUFLMUcsS0FBSzs7Ozs7O2tGQUVyQiw4REFBQ2xJLDZFQUFpQkE7a0ZBQ2I0TyxLQUFLRSxNQUFNLENBQUNuSixHQUFHLENBQ1osQ0FDSXlCLHNCQUVBLDhEQUFDbkgsMkVBQWVBO2dGQUlaNlEsY0FDSTFKLE1BQU1HLE1BQU0sS0FDWjtnRkFFSndKLG9CQUNJM0osTUFBTW9FLFdBQVc7Z0ZBRXJCd0YsY0FDSTVKLE1BQU02SixTQUFTO2dGQUVuQkMsU0FDSTlKLE1BQU1uQyxFQUFFO2dGQUVaa00sZ0JBQWdCLElBQ1o1QixzQkFDSW5JLE9BQ0F3SCxNQUNBO2dGQUdSd0Msa0JBQ0kzQixlQUNJckksV0FFSjtnRkFFSmlLLGlCQUFpQixJQUNiOUIsc0JBQ0luSSxPQUNBd0gsTUFDQTtnRkFHUjBDLG1CQUNJN0IsZUFDSXJJLFdBRUo7Z0ZBRUptSyxlQUFlLElBQ1g1QixpQkFDSXZJO2dGQUdSSyxTQUFTb0ksV0FDTHpJO2dGQUVKb0ssY0FDSXZRLGFBQ0E7Z0ZBRUo4QyxhQUNJQTtnRkFFSjBOLGVBQ0k1SjtnRkFFSjZKLGFBQWE7b0ZBQ1R6TSxJQUFJaEU7b0ZBQ0owUSxhQUNJO2dGQUNSOytFQS9ESXZLLE1BQU1uQyxFQUFFOzs7Ozs7Ozs7OzsrREFYZjJKLEtBQUszSixFQUFFOzs7Ozs7Ozs7O2tFQW9GeEMsOERBQUNtTDt3REFBSUQsV0FBVTtrRUFDWCw0RUFBQ3RSLGdEQUFNQTs0REFDSG9HLElBQUc7NERBQ0g0TCxhQUFZOzREQUNaVixXQUFVOzREQUNWOUcsb0JBQ0lBOzREQUVKM0csU0FBU0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQWM3Qiw4REFBQ25ELGdFQUFTQTtnQ0FBQzRRLFdBQVU7Ozs7OzswQ0FDckIsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDWCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQW9DOzs7Ozs7a0RBR25ELDhEQUFDQzt3Q0FBSUQsV0FBVTtrREFDVmhPLHNCQUNHQSxtQkFBbUJ3RCxHQUFHLENBQ2xCLENBQUNvQixRQUFhNEc7Z0RBb0JNdEw7aUVBbkJoQiw4REFBQytOO2dEQUNHRCxXQUFVOzBEQUVWLDRFQUFDdFEsa0VBQVlBO29EQUNUc1EsV0FBVTtvREFDVnBKLFFBQVFBLE9BQU9kLEtBQUs7b0RBQ3BCL0UsVUFBVTZGLE9BQU9YLEtBQUs7b0RBQ3RCcUgsb0JBQW9CLENBQ2hCM0IsV0FDQS9FLFFBQ0E3RixXQUVBdU0sbUJBQ0kzQixXQUNBL0UsbUJBQUFBLG9CQUFBQSxTQUFVLElBQ1Y3RixZQUFZO29EQUdwQjRLLFdBQVc7d0RBQ1A3RyxFQUFFLEdBQUU1Qyx5QkFBQUEsaUJBQWlCMEcsSUFBSSxDQUNyQixDQUFDNkksTUFDR0EsSUFBSTlLLFFBQVEsS0FDWkMsT0FBT1gsS0FBSyxlQUhoQi9ELDZDQUFBQSx1QkFJRGtELEVBQUU7b0RBQ1Q7Ozs7OzsrQ0F0QkNvSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBZ0N6Qyw4REFBQzdOLHNFQUFhQTs7a0NBQ1YsOERBQUNGLDBEQUFNQTt3QkFDSDZMLFNBQVE7d0JBQ1I2RSxTQUFTTjt3QkFDVDZCLFVBQVV6UixzRkFBU0E7a0NBQUU7Ozs7OztrQ0FTekIsOERBQUNSLDBEQUFNQTt3QkFDSDBRLFNBQVNuRjt3QkFDVDJHLFVBQ0luSix3Q0FDQWE7a0NBRUh2SSxlQUFlLElBQUksbUJBQW1COzs7Ozs7Ozs7Ozs7MEJBRy9DLDhEQUFDekIsd0RBQUtBO2dCQUFDdVMsTUFBTW5QO2dCQUFtQm9QLGNBQWNuUDswQkFDMUMsNEVBQUNwRCwrREFBWUE7b0JBQUN3UyxNQUFLO29CQUFROUIsV0FBVTs7c0NBQ2pDLDhEQUFDelEsOERBQVdBO3NDQUNSLDRFQUFDQyw2REFBVUE7MENBQUM7Ozs7Ozs7Ozs7O3dCQUdmZ0MsWUFDR2EsY0FDS2tFLE1BQU0sQ0FDSCxDQUFDa0k7Z0NBQ0dqTjttQ0FBQUEsQ0FBQUEscUJBQUFBLGdDQUFBQSwwQkFBQUEsU0FBVStCLGFBQWEsY0FBdkIvQiw4Q0FBQUEsd0JBQXlCZ0YsUUFBUSxDQUM3QmlJLEtBQUszSixFQUFFLE1BQ04ySixLQUFLVSxTQUFTOzJCQUUxQjNKLEdBQUcsQ0FBQyxDQUFDaUoscUJBQ0YsOERBQUN3QjtnQ0FFR0QsV0FBVTs7a0RBQ1YsOERBQUM5UCwrQ0FBRUE7a0RBQUV1TyxLQUFLMUcsS0FBSzs7Ozs7O2tEQUVmLDhEQUFDa0k7d0NBQ0c4Qix5QkFBeUI7NENBQ3JCQyxRQUFRdkQsS0FBS1UsU0FBUzt3Q0FDMUI7Ozs7Ozs7K0JBUENWLEtBQUszSixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBCQWFwQyw4REFBQzNFLHFFQUFXQTtnQkFDUnlSLE1BQU16TztnQkFDTjBPLGNBQWN6TzswQkFDZCw0RUFBQzlDLDRFQUFrQkE7O3NDQUNmLDhEQUFDRywyRUFBaUJBOzs4Q0FDZCw4REFBQ0MsMEVBQWdCQTs4Q0FBQzs7Ozs7OzhDQUNsQiw4REFBQ0gsZ0ZBQXNCQTs4Q0FBQzs7Ozs7Ozs7Ozs7O3NDQUk1Qiw4REFBQ0ksOERBQVFBOzRCQUNMc0YsT0FBT3REOzRCQUNQeU4sVUFBVU47NEJBQ1ZZLGFBQVk7NEJBQ1p1QixNQUFNOzs7Ozs7c0NBRVYsOERBQUN6UiwyRUFBaUJBOzs4Q0FDZCw4REFBQ0gsMkVBQWlCQTs4Q0FBQzs7Ozs7OzhDQUNuQiw4REFBQ0QsMkVBQWlCQTtvQ0FBQytQLFNBQVNSOzhDQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVF2RTtHQTcwQ005Tzs7UUFXYXBDLHVEQUFTQTtRQUNOdUIsdURBQVFBO1FBMkJHeEIseURBQVlBO1FBYU5BLHlEQUFZQTtRQWFmQSx5REFBWUE7UUFnQm5CQSx5REFBWUE7UUFtSmRBLHlEQUFZQTtRQTZCL0JELHdEQUFXQTtRQTJDWEEsd0RBQVdBO1FBcUVYQyx5REFBWUE7UUF1QlpELHdEQUFXQTtRQVNYQSx3REFBV0E7UUE2RThCQSx3REFBV0E7UUEwQlhBLHdEQUFXQTtRQXNMYkMseURBQVlBO1FBT25ERCx3REFBV0E7UUFzQlhBLHdEQUFXQTs7O0tBM3NCYnNDO0FBKzBDTiwrREFBZUEsWUFBWUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL2NyZXctdHJhaW5pbmcvY3JlYXRlL2Zvcm0udHN4P2JhMDAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgZGF5anMgZnJvbSAnZGF5anMnXHJcbmltcG9ydCB7IGlzRW1wdHkgfSBmcm9tICdsb2Rhc2gnXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCBDcmV3RHJvcGRvd24gZnJvbSAnLi4vLi4vLi4vLi4vY29tcG9uZW50cy9maWx0ZXIvY29tcG9uZW50cy9jcmV3LWRyb3Bkb3duL2NyZXctZHJvcGRvd24nXHJcbmltcG9ydCB7IFRyYWluaW5nU2Vzc2lvbkZvcm1Ta2VsZXRvbiB9IGZyb20gJy4uLy4uLy4uLy4uL2NvbXBvbmVudHMvc2tlbGV0b25zJ1xyXG5pbXBvcnQgVHJhaW5pbmdUeXBlTXVsdGlTZWxlY3REcm9wZG93biBmcm9tICcuLi90eXBlLW11bHRpc2VsZWN0LWRyb3Bkb3duJ1xyXG5pbXBvcnQgQ3Jld011bHRpU2VsZWN0RHJvcGRvd24gZnJvbSAnLi4vLi4vY3Jldy9tdWx0aXNlbGVjdC1kcm9wZG93bi9tdWx0aXNlbGVjdC1kcm9wZG93bidcclxuaW1wb3J0IHtcclxuICAgIENSRUFURV9UUkFJTklOR19TRVNTSU9OLFxyXG4gICAgVVBEQVRFX1RSQUlOSU5HX1NFU1NJT04sXHJcbiAgICBDUkVBVEVfTUVNQkVSX1RSQUlOSU5HX1NJR05BVFVSRSxcclxuICAgIFVQREFURV9NRU1CRVJfVFJBSU5JTkdfU0lHTkFUVVJFLFxyXG4gICAgQ1JFQVRFX1RSQUlOSU5HX1NFU1NJT05fRFVFLFxyXG4gICAgVVBEQVRFX1RSQUlOSU5HX1NFU1NJT05fRFVFLFxyXG4gICAgQ1JFQVRFX0NVU1RPTUlTRURfQ09NUE9ORU5UX0ZJRUxEX0RBVEEsXHJcbiAgICBVUERBVEVfQ1VTVE9NSVNFRF9DT01QT05FTlRfRklFTERfREFUQSxcclxufSBmcm9tICdAL2FwcC9saWIvZ3JhcGhRTC9tdXRhdGlvbidcclxuaW1wb3J0IHtcclxuICAgIEdFVF9NRU1CRVJfVFJBSU5JTkdfU0lHTkFUVVJFUyxcclxuICAgIEdFVF9TRUNUSU9OX01FTUJFUl9JTUFHRVMsXHJcbiAgICBSRUFEX09ORV9UUkFJTklOR19TRVNTSU9OX0RVRSxcclxuICAgIENSRVdfVFJBSU5JTkdfVFlQRVMsXHJcbiAgICBUUkFJTklOR19TRVNTSU9OX0JZX0lELFxyXG4gICAgVFJBSU5JTkdfVFlQRV9CWV9JRCxcclxufSBmcm9tICdAL2FwcC9saWIvZ3JhcGhRTC9xdWVyeSdcclxuaW1wb3J0IHsgdXNlTXV0YXRpb24sIHVzZUxhenlRdWVyeSB9IGZyb20gJ0BhcG9sbG8vY2xpZW50J1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXHJcbmltcG9ydCBFZGl0b3IgZnJvbSAnLi4vLi4vZWRpdG9yJ1xyXG5pbXBvcnQgeyBTY3JvbGxBcmVhIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3Njcm9sbC1hcmVhJ1xyXG5pbXBvcnQgeyBnZXRQZXJtaXNzaW9ucywgaGFzUGVybWlzc2lvbiB9IGZyb20gJ0AvYXBwL2hlbHBlcnMvdXNlckhlbHBlcidcclxuaW1wb3J0IExvYWRpbmcgZnJvbSAnQC9hcHAvbG9hZGluZydcclxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGFiZWwnXHJcbmltcG9ydCB7IENvbWJvYm94LCBPcHRpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY29tYm9Cb3gnXHJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcclxuaW1wb3J0IHsgU2VwYXJhdG9yIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NlcGFyYXRvcidcclxuaW1wb3J0IHtcclxuICAgIFNoZWV0LFxyXG4gICAgU2hlZXRDb250ZW50LFxyXG4gICAgU2hlZXRIZWFkZXIsXHJcbiAgICBTaGVldFRpdGxlLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9zaGVldCdcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcclxuaW1wb3J0IFNpZ25hdHVyZVBhZCBmcm9tICdAL2NvbXBvbmVudHMvc2lnbmF0dXJlLXBhZCdcclxuaW1wb3J0IHsgRm9vdGVyV3JhcHBlciB9IGZyb20gJ0AvY29tcG9uZW50cy9mb290ZXItd3JhcHBlcidcclxuaW1wb3J0IFNlYUxvZ3NCdXR0b24gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NlYS1sb2dzLWJ1dHRvbidcclxuaW1wb3J0IHtcclxuICAgIENoZWNrRmllbGQsXHJcbiAgICBDaGVja0ZpZWxkQ29udGVudCxcclxuICAgIERhaWx5Q2hlY2tGaWVsZCxcclxufSBmcm9tICdAL2NvbXBvbmVudHMvZGFpbHktY2hlY2stZmllbGQnXHJcbmltcG9ydCBEYXRlUGlja2VyIGZyb20gJ0AvY29tcG9uZW50cy9EYXRlUmFuZ2UnXHJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9ob29rcy91c2UtdG9hc3QnXHJcbmltcG9ydCB7IEFycm93TGVmdCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcclxuaW1wb3J0IHsgSDQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWknXHJcbmltcG9ydCB7XHJcbiAgICBBbGVydERpYWxvZyxcclxuICAgIEFsZXJ0RGlhbG9nQWN0aW9uLFxyXG4gICAgQWxlcnREaWFsb2dDYW5jZWwsXHJcbiAgICBBbGVydERpYWxvZ0NvbnRlbnQsXHJcbiAgICBBbGVydERpYWxvZ0Rlc2NyaXB0aW9uLFxyXG4gICAgQWxlcnREaWFsb2dGb290ZXIsXHJcbiAgICBBbGVydERpYWxvZ0hlYWRlcixcclxuICAgIEFsZXJ0RGlhbG9nVGl0bGUsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2FsZXJ0LWRpYWxvZydcclxuaW1wb3J0IHsgVGV4dGFyZWEgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdGV4dGFyZWEnXHJcbmltcG9ydCB7IFJlYWRWZXNzZWxzIH0gZnJvbSAnLi9xdWVyaWVzJ1xyXG5cclxuY29uc3QgVHJhaW5pbmdGb3JtID0gKHtcclxuICAgIHRyYWluaW5nSUQgPSAwLFxyXG4gICAgbWVtYmVySWQgPSAwLFxyXG4gICAgdHJhaW5pbmdUeXBlSWQgPSAwLFxyXG4gICAgdmVzc2VsSWQgPSAwLFxyXG59OiB7XHJcbiAgICB0cmFpbmluZ0lEOiBudW1iZXJcclxuICAgIG1lbWJlcklkOiBudW1iZXJcclxuICAgIHRyYWluaW5nVHlwZUlkOiBudW1iZXJcclxuICAgIHZlc3NlbElkPzogbnVtYmVyXHJcbn0pID0+IHtcclxuICAgIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXHJcbiAgICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpXHJcbiAgICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcclxuICAgIGNvbnN0IFt0cmFpbmluZywgc2V0VHJhaW5pbmddID0gdXNlU3RhdGU8YW55Pih7fSlcclxuICAgIGNvbnN0IFtyYXdUcmFpbmluZywgc2V0UmF3VHJhaW5pbmddID0gdXNlU3RhdGU8YW55PigpXHJcbiAgICBjb25zdCBbdHJhaW5pbmdEYXRlLCBzZXRUcmFpbmluZ0RhdGVdID0gdXNlU3RhdGUobmV3IERhdGUoKSlcclxuICAgIGNvbnN0IFtoYXNGb3JtRXJyb3JzLCBzZXRIYXNGb3JtRXJyb3JzXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gICAgY29uc3QgW3NlbGVjdGVkTWVtYmVyTGlzdCwgc2V0U2VsZWN0ZWRNZW1iZXJMaXN0XSA9IHVzZVN0YXRlKFtdIGFzIGFueVtdKVxyXG4gICAgY29uc3QgW3NpZ25hdHVyZU1lbWJlcnMsIHNldFNpZ25hdHVyZU1lbWJlcnNdID0gdXNlU3RhdGUoW10gYXMgYW55W10pXHJcbiAgICBjb25zdCBbdmVzc2Vscywgc2V0VmVzc2Vsc10gPSB1c2VTdGF0ZTxhbnk+KClcclxuICAgIGNvbnN0IFt0cmFpbmluZ1R5cGVzLCBzZXRUcmFpbmluZ1R5cGVzXSA9IHVzZVN0YXRlPGFueT4oW10pXHJcbiAgICBjb25zdCBbY29udGVudCwgc2V0Q29udGVudF0gPSB1c2VTdGF0ZTxhbnk+KCcnKVxyXG4gICAgY29uc3QgW29wZW5WaWV3UHJvY2VkdXJlLCBzZXRPcGVuVmlld1Byb2NlZHVyZV0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICAgIGNvbnN0IFtjdXJyZW50Q29tbWVudCwgc2V0Q3VycmVudENvbW1lbnRdID0gdXNlU3RhdGU8YW55PignJylcclxuICAgIGNvbnN0IFtjdXJyZW50RmllbGQsIHNldEN1cnJlbnRGaWVsZF0gPSB1c2VTdGF0ZTxhbnk+KCcnKVxyXG4gICAgY29uc3QgW2J1ZmZlclByb2NlZHVyZUNoZWNrLCBzZXRCdWZmZXJQcm9jZWR1cmVDaGVja10gPSB1c2VTdGF0ZTxhbnk+KFtdKVxyXG4gICAgY29uc3QgW2J1ZmZlckZpZWxkQ29tbWVudCwgc2V0QnVmZmVyRmllbGRDb21tZW50XSA9IHVzZVN0YXRlPGFueT4oW10pXHJcbiAgICBjb25zdCBbb3BlbkNvbW1lbnRBbGVydCwgc2V0T3BlbkNvbW1lbnRBbGVydF0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICAgIGNvbnN0IFtmb3JtRXJyb3JzLCBzZXRGb3JtRXJyb3JzXSA9IHVzZVN0YXRlKHtcclxuICAgICAgICBUcmFpbmluZ1R5cGVzOiAnJyxcclxuICAgICAgICBUcmFpbmVySUQ6ICcnLFxyXG4gICAgICAgIFZlc3NlbElEOiAnJyxcclxuICAgICAgICBEYXRlOiAnJyxcclxuICAgIH0pXHJcbiAgICBjb25zdCBbdmVzc2VsSUQsIHNldFZlc3NlbElEXSA9IHVzZVN0YXRlKHZlc3NlbElkKVxyXG4gICAgY29uc3QgW2ZpZWxkSW1hZ2VzLCBzZXRGaWVsZEltYWdlc10gPSB1c2VTdGF0ZTxhbnk+KGZhbHNlKVxyXG5cclxuICAgIC8vIEdyYXBoUUwgcXVlcmllcyB0byByZXBsYWNlIHRoZSBwcm9ibGVtYXRpYyBmdW5jdGlvbiBjYWxsc1xyXG4gICAgY29uc3QgW3F1ZXJ5VHJhaW5pbmdUeXBlc10gPSB1c2VMYXp5UXVlcnkoQ1JFV19UUkFJTklOR19UWVBFUywge1xyXG4gICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZFRyYWluaW5nVHlwZXMubm9kZXNcclxuICAgICAgICAgICAgaWYgKGRhdGEpIHtcclxuICAgICAgICAgICAgICAgIHNldFRyYWluaW5nVHlwZXMoZGF0YSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcigncXVlcnlUcmFpbmluZ1R5cGVzIGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcblxyXG4gICAgY29uc3QgW3F1ZXJ5VHJhaW5pbmdTZXNzaW9uQnlJRF0gPSB1c2VMYXp5UXVlcnkoVFJBSU5JTkdfU0VTU0lPTl9CWV9JRCwge1xyXG4gICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZE9uZVRyYWluaW5nU2Vzc2lvblxyXG4gICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgaGFuZGxlU2V0VHJhaW5pbmcoZGF0YSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcigncXVlcnlUcmFpbmluZ1Nlc3Npb24gZXJyb3InLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuXHJcbiAgICBjb25zdCBbcXVlcnlUcmFpbmluZ1R5cGVCeUlEXSA9IHVzZUxhenlRdWVyeShUUkFJTklOR19UWVBFX0JZX0lELCB7XHJcbiAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkT25lVHJhaW5pbmdUeXBlXHJcbiAgICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRUcmFpbmluZygocHJldlRyYWluaW5nOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4ucHJldlRyYWluaW5nLFxyXG4gICAgICAgICAgICAgICAgICAgIFRyYWluaW5nVHlwZXM6IFtkYXRhLmlkXSxcclxuICAgICAgICAgICAgICAgIH0pKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdxdWVyeVRyYWluaW5nVHlwZSBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG5cclxuICAgIGNvbnN0IFtnZXRGaWVsZEltYWdlc10gPSB1c2VMYXp5UXVlcnkoR0VUX1NFQ1RJT05fTUVNQkVSX0lNQUdFUywge1xyXG4gICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZENhcHR1cmVJbWFnZXMubm9kZXNcclxuICAgICAgICAgICAgaWYgKGRhdGEpIHtcclxuICAgICAgICAgICAgICAgIHNldEZpZWxkSW1hZ2VzKGRhdGEpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ2dldEZpZWxkSW1hZ2VzIGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcblxyXG4gICAgLy8gRGVmaW5lIGhhbmRsZVNldFRyYWluaW5nIGZ1bmN0aW9uXHJcbiAgICBjb25zdCBoYW5kbGVTZXRUcmFpbmluZyA9IHVzZUNhbGxiYWNrKFxyXG4gICAgICAgICh0cmFpbmluZzogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHREYXRlID0gbmV3IERhdGUodHJhaW5pbmcuZGF0ZSlcclxuICAgICAgICAgICAgY29uc3QgdHJhaW5pbmdEYXRhID0ge1xyXG4gICAgICAgICAgICAgICAgSUQ6IHRyYWluaW5nSUQsXHJcbiAgICAgICAgICAgICAgICBEYXRlOiBkYXlqcyh0cmFpbmluZy5kYXRlKS5mb3JtYXQoJ1lZWVktTU0tREQnKSxcclxuICAgICAgICAgICAgICAgIE1lbWJlcnM6IHRyYWluaW5nLm1lbWJlcnMubm9kZXMubWFwKChtOiBhbnkpID0+IG0uaWQpLFxyXG4gICAgICAgICAgICAgICAgVHJhaW5lcklEOiB0cmFpbmluZy50cmFpbmVyLmlkLFxyXG4gICAgICAgICAgICAgICAgVHJhaW5pbmdTdW1tYXJ5OiB0cmFpbmluZy50cmFpbmluZ1N1bW1hcnksXHJcbiAgICAgICAgICAgICAgICBUcmFpbmluZ1R5cGVzOiB0cmFpbmluZy50cmFpbmluZ1R5cGVzLm5vZGVzLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAodDogYW55KSA9PiB0LmlkLFxyXG4gICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgIC8vIFZlc3NlbElEOiB0cmFpbmluZy52ZXNzZWwuaWQsXHJcbiAgICAgICAgICAgICAgICBWZXNzZWxJRDogdHJhaW5pbmcudmVzc2VsSUQsXHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGNvbnN0IG1lbWJlcnMgPVxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmcubWVtYmVycy5ub2Rlcy5tYXAoKG06IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICBsYWJlbDogYCR7bS5maXJzdE5hbWUgPz8gJyd9ICR7bS5zdXJuYW1lID8/ICcnfWAsXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IG0uaWQsXHJcbiAgICAgICAgICAgICAgICB9KSkgfHwgW11cclxuXHJcbiAgICAgICAgICAgIGNvbnN0IHZlc3NlbENyZXdJZHMgPSB0cmFpbmluZy52ZXNzZWwuc2VhTG9nc01lbWJlcnMubm9kZXMubWFwKFxyXG4gICAgICAgICAgICAgICAgKHNsbTogYW55KSA9PiArc2xtLmlkLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIGNvbnN0IHZlc3NlbENyZXdzID0gbWVtYmVycy5maWx0ZXIoKG06IGFueSkgPT5cclxuICAgICAgICAgICAgICAgIHZlc3NlbENyZXdJZHMuaW5jbHVkZXMoK20udmFsdWUpLFxyXG4gICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICBjb25zdCBzaWduYXR1cmVzID0gdHJhaW5pbmcuc2lnbmF0dXJlcy5ub2Rlcy5tYXAoKHM6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgICAgIE1lbWJlcklEOiBzLm1lbWJlci5pZCxcclxuICAgICAgICAgICAgICAgIFNpZ25hdHVyZURhdGE6IHMuc2lnbmF0dXJlRGF0YSxcclxuICAgICAgICAgICAgICAgIElEOiBzLmlkLFxyXG4gICAgICAgICAgICB9KSlcclxuXHJcbiAgICAgICAgICAgIC8vIEJhdGNoIGFsbCBzdGF0ZSB1cGRhdGVzIHVzaW5nIFJlYWN0J3MgYXV0b21hdGljIGJhdGNoaW5nXHJcbiAgICAgICAgICAgIHNldFRyYWluaW5nRGF0ZSh0RGF0ZSlcclxuICAgICAgICAgICAgc2V0UmF3VHJhaW5pbmcodHJhaW5pbmcpXHJcbiAgICAgICAgICAgIHNldFRyYWluaW5nKHRyYWluaW5nRGF0YSlcclxuICAgICAgICAgICAgc2V0Q29udGVudCh0cmFpbmluZy50cmFpbmluZ1N1bW1hcnkpXHJcbiAgICAgICAgICAgIHNldFNlbGVjdGVkTWVtYmVyTGlzdCh2ZXNzZWxDcmV3cylcclxuICAgICAgICAgICAgc2V0U2lnbmF0dXJlTWVtYmVycyhzaWduYXR1cmVzKVxyXG5cclxuICAgICAgICAgICAgLy8gSW5pdGlhbGl6ZSBidWZmZXIgd2l0aCBleGlzdGluZyBwcm9jZWR1cmUgZmllbGQgZGF0YSBmb3IgdXBkYXRlc1xyXG4gICAgICAgICAgICBpZiAodHJhaW5pbmcucHJvY2VkdXJlRmllbGRzPy5ub2Rlcykge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdQcm9jZWR1cmVDaGVja3MgPVxyXG4gICAgICAgICAgICAgICAgICAgIHRyYWluaW5nLnByb2NlZHVyZUZpZWxkcy5ub2Rlcy5tYXAoKGZpZWxkOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkSWQ6IGZpZWxkLmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZElELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IGZpZWxkLnN0YXR1cyA9PT0gJ09rJyxcclxuICAgICAgICAgICAgICAgICAgICB9KSlcclxuICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nRmllbGRDb21tZW50cyA9IHRyYWluaW5nLnByb2NlZHVyZUZpZWxkcy5ub2Rlc1xyXG4gICAgICAgICAgICAgICAgICAgIC5maWx0ZXIoKGZpZWxkOiBhbnkpID0+IGZpZWxkLmNvbW1lbnQpXHJcbiAgICAgICAgICAgICAgICAgICAgLm1hcCgoZmllbGQ6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZmllbGRJZDogZmllbGQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbW1lbnQ6IGZpZWxkLmNvbW1lbnQsXHJcbiAgICAgICAgICAgICAgICAgICAgfSkpXHJcblxyXG4gICAgICAgICAgICAgICAgc2V0QnVmZmVyUHJvY2VkdXJlQ2hlY2soZXhpc3RpbmdQcm9jZWR1cmVDaGVja3MpXHJcbiAgICAgICAgICAgICAgICBzZXRCdWZmZXJGaWVsZENvbW1lbnQoZXhpc3RpbmdGaWVsZENvbW1lbnRzKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBbdHJhaW5pbmdJRF0sXHJcbiAgICApXHJcblxyXG4gICAgLy8gdXNlRWZmZWN0IGhvb2tzIHRvIHJlcGxhY2UgdGhlIHByb2JsZW1hdGljIGZ1bmN0aW9uIGNhbGxzXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIC8vIExvYWQgdHJhaW5pbmcgdHlwZXMgb24gY29tcG9uZW50IG1vdW50XHJcbiAgICAgICAgcXVlcnlUcmFpbmluZ1R5cGVzKClcclxuICAgIH0sIFtdKSAvLyBFbXB0eSBkZXBlbmRlbmN5IGFycmF5IC0gb25seSBydW4gb25jZSBvbiBtb3VudFxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgLy8gTG9hZCB0cmFpbmluZyBzZXNzaW9uIGJ5IElEIHdoZW4gdHJhaW5pbmdJRCBjaGFuZ2VzXHJcbiAgICAgICAgaWYgKHRyYWluaW5nSUQgPiAwKSB7XHJcbiAgICAgICAgICAgIHF1ZXJ5VHJhaW5pbmdTZXNzaW9uQnlJRCh7XHJcbiAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICBpZDogdHJhaW5pbmdJRCxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG4gICAgfSwgW3RyYWluaW5nSURdKSAvLyBPbmx5IGRlcGVuZCBvbiB0cmFpbmluZ0lEXHJcblxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICAvLyBMb2FkIHRyYWluaW5nIHR5cGUgYnkgSUQgd2hlbiB0cmFpbmluZ1R5cGVJZCBjaGFuZ2VzXHJcbiAgICAgICAgaWYgKHRyYWluaW5nVHlwZUlkID4gMCkge1xyXG4gICAgICAgICAgICBxdWVyeVRyYWluaW5nVHlwZUJ5SUQoe1xyXG4gICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWQ6IHRyYWluaW5nVHlwZUlkLFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9XHJcbiAgICB9LCBbdHJhaW5pbmdUeXBlSWRdKSAvLyBPbmx5IGRlcGVuZCBvbiB0cmFpbmluZ1R5cGVJZFxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKHRyYWluaW5nSUQgPiAwKSB7XHJcbiAgICAgICAgICAgIGdldEZpZWxkSW1hZ2VzKHtcclxuICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgIGZpbHRlcjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1Nlc3Npb25JRDogeyBlcTogdHJhaW5pbmdJRCB9LFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgIH1cclxuICAgIH0sIFt0cmFpbmluZ0lEXSkgLy8gT25seSBkZXBlbmQgb24gdHJhaW5pbmdJRFxyXG5cclxuICAgIGNvbnN0IHJlZnJlc2hJbWFnZXMgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgYXdhaXQgZ2V0RmllbGRJbWFnZXMoe1xyXG4gICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgIGZpbHRlcjoge1xyXG4gICAgICAgICAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbklEOiB7IGVxOiB0cmFpbmluZ0lEIH0sXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcbiAgICB9LCBbZ2V0RmllbGRJbWFnZXMsIHRyYWluaW5nSURdKVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVNldFZlc3NlbHMgPSAoZGF0YTogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc3QgYWN0aXZlVmVzc2VscyA9IGRhdGE/LmZpbHRlcigodmVzc2VsOiBhbnkpID0+ICF2ZXNzZWwuYXJjaGl2ZWQpXHJcbiAgICAgICAgY29uc3QgZm9ybWF0dGVkRGF0YSA9IFtcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgbGFiZWw6ICdPdGhlcicsXHJcbiAgICAgICAgICAgICAgICB2YWx1ZTogJ090aGVyJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgbGFiZWw6ICdEZXNrdG9wL3Nob3JlJyxcclxuICAgICAgICAgICAgICAgIHZhbHVlOiAnT25zaG9yZScsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIC4uLmFjdGl2ZVZlc3NlbHMubWFwKCh2ZXNzZWw6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgICAgIHZhbHVlOiB2ZXNzZWwuaWQsXHJcbiAgICAgICAgICAgICAgICBsYWJlbDogdmVzc2VsLnRpdGxlLFxyXG4gICAgICAgICAgICB9KSksXHJcbiAgICAgICAgXVxyXG4gICAgICAgIHNldFZlc3NlbHMoZm9ybWF0dGVkRGF0YSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbcXVlcnlWZXNzZWxzXSA9IHVzZUxhenlRdWVyeShSZWFkVmVzc2Vscywge1xyXG4gICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocXVlcnlWZXNzZWxSZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGlmIChxdWVyeVZlc3NlbFJlc3BvbnNlLnJlYWRWZXNzZWxzLm5vZGVzKSB7XHJcbiAgICAgICAgICAgICAgICBoYW5kbGVTZXRWZXNzZWxzKHF1ZXJ5VmVzc2VsUmVzcG9uc2UucmVhZFZlc3NlbHMubm9kZXMpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5VmVzc2VscyBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAoaXNMb2FkaW5nKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGxvYWRWZXNzZWxzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgYXdhaXQgcXVlcnlWZXNzZWxzKHtcclxuICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGltaXQ6IDIwMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAwLFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGxvYWRWZXNzZWxzKClcclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtpc0xvYWRpbmcsIHF1ZXJ5VmVzc2Vsc10pXHJcblxyXG4gICAgY29uc3QgW1xyXG4gICAgICAgIG11dGF0aW9uQ3JlYXRlVHJhaW5pbmdTZXNzaW9uLFxyXG4gICAgICAgIHsgbG9hZGluZzogbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb25Mb2FkaW5nIH0sXHJcbiAgICBdID0gdXNlTXV0YXRpb24oQ1JFQVRFX1RSQUlOSU5HX1NFU1NJT04sIHtcclxuICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmNyZWF0ZVRyYWluaW5nU2Vzc2lvblxyXG4gICAgICAgICAgICBpZiAoZGF0YS5pZCA+IDApIHtcclxuICAgICAgICAgICAgICAgIGlmIChidWZmZXJQcm9jZWR1cmVDaGVjay5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvY2VkdXJlRmllbGRzID0gYnVmZmVyUHJvY2VkdXJlQ2hlY2subWFwKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAocHJvY2VkdXJlRmllbGQ6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHByb2NlZHVyZUZpZWxkLnN0YXR1cyA/ICdPaycgOiAnTm90X09rJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1Nlc3Npb25JRDogZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXN0b21pc2VkQ29tcG9uZW50RmllbGRJRDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuZmllbGRJZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb21tZW50OiBidWZmZXJGaWVsZENvbW1lbnQuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGNvbW1lbnQ6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbW1lbnQuZmllbGRJZCA9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuZmllbGRJZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApPy5jb21tZW50LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICBwcm9jZWR1cmVGaWVsZHMuZm9yRWFjaCgocHJvY2VkdXJlRmllbGQ6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjcmVhdGVDdXN0b21pc2VkQ29tcG9uZW50RmllbGREYXRhKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0OiBwcm9jZWR1cmVGaWVsZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZXMoKVxyXG4gICAgICAgICAgICAgICAgdXBkYXRlU2lnbmF0dXJlcyhkYXRhLmlkKVxyXG4gICAgICAgICAgICAgICAgaGFuZGxlRWRpdG9yQ2hhbmdlKGRhdGEudHJhaW5pbmdTdW1tYXJ5KVxyXG4gICAgICAgICAgICAgICAgcm91dGVyLnB1c2goJy9jcmV3LXRyYWluaW5nJylcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ211dGF0aW9uQ3JlYXRlVXNlciBlcnJvcicsIHJlc3BvbnNlKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdtdXRhdGlvbkNyZWF0ZVRyYWluaW5nU2Vzc2lvbiBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG4gICAgY29uc3QgW1xyXG4gICAgICAgIG11dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uLFxyXG4gICAgICAgIHsgbG9hZGluZzogbXV0YXRpb25VcGRhdGVUcmFpbmluZ1Nlc3Npb25Mb2FkaW5nIH0sXHJcbiAgICBdID0gdXNlTXV0YXRpb24oVVBEQVRFX1RSQUlOSU5HX1NFU1NJT04sIHtcclxuICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnVwZGF0ZVRyYWluaW5nU2Vzc2lvblxyXG4gICAgICAgICAgICBpZiAoZGF0YS5pZCA+IDApIHtcclxuICAgICAgICAgICAgICAgIC8vIEhhbmRsZSBwcm9jZWR1cmUgY2hlY2tzIGZvciB1cGRhdGVzXHJcbiAgICAgICAgICAgICAgICBpZiAoYnVmZmVyUHJvY2VkdXJlQ2hlY2subGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHByb2NlZHVyZUZpZWxkcyA9IGJ1ZmZlclByb2NlZHVyZUNoZWNrLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgKHByb2NlZHVyZUZpZWxkOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nRmllbGQgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJhd1RyYWluaW5nPy5wcm9jZWR1cmVGaWVsZHM/Lm5vZGVzPy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZmllbGQ6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkLmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZElEID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuZmllbGRJZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogZXhpc3RpbmdGaWVsZD8uaWQsIC8vIEluY2x1ZGUgSUQgZm9yIHVwZGF0ZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHByb2NlZHVyZUZpZWxkLnN0YXR1cyA/ICdPaycgOiAnTm90X09rJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1Nlc3Npb25JRDogZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXN0b21pc2VkQ29tcG9uZW50RmllbGRJRDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuZmllbGRJZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb21tZW50OiBidWZmZXJGaWVsZENvbW1lbnQuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGNvbW1lbnQ6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbW1lbnQuZmllbGRJZCA9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuZmllbGRJZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApPy5jb21tZW50LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICBwcm9jZWR1cmVGaWVsZHMuZm9yRWFjaCgocHJvY2VkdXJlRmllbGQ6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocHJvY2VkdXJlRmllbGQuaWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyBmaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlQ3VzdG9taXNlZENvbXBvbmVudEZpZWxkRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0OiBwcm9jZWR1cmVGaWVsZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIENyZWF0ZSBuZXcgZmllbGRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgaWQsIC4uLmNyZWF0ZUlucHV0IH0gPSBwcm9jZWR1cmVGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3JlYXRlQ3VzdG9taXNlZENvbXBvbmVudEZpZWxkRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0OiBjcmVhdGVJbnB1dCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB1cGRhdGVUcmFpbmluZ1Nlc3Npb25EdWVzKClcclxuICAgICAgICAgICAgICAgIHVwZGF0ZVNpZ25hdHVyZXModHJhaW5pbmdJRClcclxuICAgICAgICAgICAgICAgIGhhbmRsZUVkaXRvckNoYW5nZShkYXRhLnRyYWluaW5nU3VtbWFyeSlcclxuICAgICAgICAgICAgICAgIGlmICgrbWVtYmVySWQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcm91dGVyLnB1c2goYC9jcmV3L2luZm8/aWQ9JHttZW1iZXJJZH1gKVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmICgrdmVzc2VsSWQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcm91dGVyLnB1c2goYC92ZXNzZWwvaW5mbz9pZD0ke3Zlc3NlbElkfWApXHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvY3Jldy10cmFpbmluZycpXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdtdXRhdGlvblVwZGF0ZVRyYWluaW5nU2Vzc2lvbiBlcnJvcicsIHJlc3BvbnNlKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdtdXRhdGlvblVwZGF0ZVRyYWluaW5nU2Vzc2lvbiBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG4gICAgY29uc3QgW1xyXG4gICAgICAgIHJlYWRPbmVUcmFpbmluZ1Nlc3Npb25EdWUsXHJcbiAgICAgICAgeyBsb2FkaW5nOiByZWFkT25lVHJhaW5pbmdTZXNzaW9uRHVlTG9hZGluZyB9LFxyXG4gICAgXSA9IHVzZUxhenlRdWVyeShSRUFEX09ORV9UUkFJTklOR19TRVNTSU9OX0RVRSwge1xyXG4gICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2UucmVhZE9uZVRyYWluaW5nU2Vzc2lvbkR1ZS5kYXRhXHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdyZWFkT25lVHJhaW5pbmdTZXNzaW9uRHVlTG9hZGluZyBlcnJvcjonLCBlcnJvcilcclxuICAgICAgICAgICAgcmV0dXJuIG51bGxcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuICAgIGNvbnN0IGdldFRyYWluaW5nU2Vzc2lvbkR1ZVdpdGhWYXJpYWJsZXMgPSBhc3luYyAoXHJcbiAgICAgICAgdmFyaWFibGVzOiBhbnkgPSB7fSxcclxuICAgICAgICBvbkNvbXBsZXRlZDogYW55LFxyXG4gICAgKSA9PiB7XHJcbiAgICAgICAgY29uc3QgeyBkYXRhIH06IGFueSA9IGF3YWl0IHJlYWRPbmVUcmFpbmluZ1Nlc3Npb25EdWUoe1xyXG4gICAgICAgICAgICB2YXJpYWJsZXM6IHZhcmlhYmxlcyxcclxuICAgICAgICB9KVxyXG4gICAgICAgIG9uQ29tcGxldGVkKGRhdGEucmVhZE9uZVRyYWluaW5nU2Vzc2lvbkR1ZSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbXHJcbiAgICAgICAgbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb25EdWUsXHJcbiAgICAgICAgeyBsb2FkaW5nOiBjcmVhdGVUcmFpbmluZ1Nlc3Npb25EdWVMb2FkaW5nIH0sXHJcbiAgICBdID0gdXNlTXV0YXRpb24oQ1JFQVRFX1RSQUlOSU5HX1NFU1NJT05fRFVFLCB7XHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7fSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdjcmVhdGVUcmFpbmluZ1Nlc3Npb25EdWUgZXJyb3InLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuICAgIGNvbnN0IFtcclxuICAgICAgICBtdXRhdGlvblVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZSxcclxuICAgICAgICB7IGxvYWRpbmc6IHVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZUxvYWRpbmcgfSxcclxuICAgIF0gPSB1c2VNdXRhdGlvbihVUERBVEVfVFJBSU5JTkdfU0VTU0lPTl9EVUUsIHtcclxuICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHt9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3VwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZSBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG4gICAgY29uc3QgdXBkYXRlVHJhaW5pbmdTZXNzaW9uRHVlcyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgICBjb25zdCB0cmFpbmluZ1Nlc3Npb25EdWVzOiBhbnkgPSBbXVxyXG4gICAgICAgIGNvbnN0IHZlc3NlbElEID0gdHJhaW5pbmcuVmVzc2VsSURcclxuICAgICAgICB0cmFpbmluZy5UcmFpbmluZ1R5cGVzLmZvckVhY2goKHQ6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCB0cmFpbmluZ0luZm8gPSB0cmFpbmluZ1R5cGVzLmZpbmQoKHR0OiBhbnkpID0+IHR0LmlkID09PSB0KVxyXG5cclxuICAgICAgICAgICAgaWYgKCFpc0VtcHR5KHRyYWluaW5nSW5mbykgJiYgdHJhaW5pbmdJbmZvLm9jY3Vyc0V2ZXJ5ID4gMCkge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdHJhaW5pbmdUeXBlSUQgPSB0XHJcbiAgICAgICAgICAgICAgICBjb25zdCBuZXdEdWVEYXRlID0gZGF5anModHJhaW5pbmcuRGF0ZSkuYWRkKFxyXG4gICAgICAgICAgICAgICAgICAgIHRyYWluaW5nSW5mby5vY2N1cnNFdmVyeSxcclxuICAgICAgICAgICAgICAgICAgICAnZGF5JyxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIHRyYWluaW5nLk1lbWJlcnMuZm9yRWFjaCgobTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWVtYmVySUQgPSBtXHJcbiAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uRHVlcy5wdXNoKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZHVlRGF0ZTogbmV3RHVlRGF0ZS5mb3JtYXQoJ1lZWVktTU0tREQnKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVySUQ6IG1lbWJlcklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxJRDogdmVzc2VsSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nVHlwZUlEOiB0cmFpbmluZ1R5cGVJRCxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgbGV0IHRyYWluaW5nU2Vzc2lvbkR1ZVdpdGhJRHM6IGFueSA9IFtdXHJcbiAgICAgICAgaWYgKCFpc0VtcHR5KHRyYWluaW5nU2Vzc2lvbkR1ZXMpKSB7XHJcbiAgICAgICAgICAgIGF3YWl0IFByb21pc2UuYWxsKFxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uRHVlcy5tYXAoYXN5bmMgKGl0ZW06IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhcmlhYmxlcyA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJJRDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVxOiBpdGVtLm1lbWJlcklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbElEOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXE6IGl0ZW0udmVzc2VsSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcTogaXRlbS50cmFpbmluZ1R5cGVJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG9uQ29tcGxldGVkID0gKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uRHVlV2l0aElEcy5wdXNoKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLml0ZW0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogcmVzcG9uc2U/LmlkID8/IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBhd2FpdCBnZXRUcmFpbmluZ1Nlc3Npb25EdWVXaXRoVmFyaWFibGVzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ29tcGxldGVkLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAoIWlzRW1wdHkodHJhaW5pbmdTZXNzaW9uRHVlV2l0aElEcykpIHtcclxuICAgICAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoXHJcbiAgICAgICAgICAgICAgICBBcnJheS5mcm9tKHRyYWluaW5nU2Vzc2lvbkR1ZVdpdGhJRHMpLm1hcChhc3luYyAoaXRlbTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFyaWFibGVzID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHsgaW5wdXQ6IGl0ZW0gfSxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGl0ZW0uaWQgPT09IDApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb25EdWUodmFyaWFibGVzKVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IG11dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uRHVlKHZhcmlhYmxlcylcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9KSxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbY3JlYXRlQ3VzdG9taXNlZENvbXBvbmVudEZpZWxkRGF0YV0gPSB1c2VNdXRhdGlvbihcclxuICAgICAgICBDUkVBVEVfQ1VTVE9NSVNFRF9DT01QT05FTlRfRklFTERfREFUQSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmNyZWF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGFcclxuICAgICAgICAgICAgICAgIGlmIChkYXRhLmlkID4gMCAmJiByYXdUcmFpbmluZz8ucHJvY2VkdXJlRmllbGRzPy5ub2Rlcykge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFJhd1RyYWluaW5nKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmF3VHJhaW5pbmcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb2NlZHVyZUZpZWxkczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmF3VHJhaW5pbmcucHJvY2VkdXJlRmllbGRzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbm9kZXM6IFsuLi5yYXdUcmFpbmluZy5wcm9jZWR1cmVGaWVsZHMubm9kZXMsIGRhdGFdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICdjcmVhdGVDdXN0b21pc2VkQ29tcG9uZW50RmllbGREYXRhIGVycm9yJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignY3JlYXRlQ3VzdG9taXNlZENvbXBvbmVudEZpZWxkRGF0YSBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICApXHJcblxyXG4gICAgY29uc3QgW3VwZGF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGFdID0gdXNlTXV0YXRpb24oXHJcbiAgICAgICAgVVBEQVRFX0NVU1RPTUlTRURfQ09NUE9ORU5UX0ZJRUxEX0RBVEEsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS51cGRhdGVDdXN0b21pc2VkQ29tcG9uZW50RmllbGREYXRhXHJcbiAgICAgICAgICAgICAgICBpZiAoZGF0YS5pZCA+IDApIHtcclxuICAgICAgICAgICAgICAgICAgICBzZXRSYXdUcmFpbmluZyh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnJhd1RyYWluaW5nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwcm9jZWR1cmVGaWVsZHM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnJhd1RyYWluaW5nLnByb2NlZHVyZUZpZWxkcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5vZGVzOiBbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmF3VHJhaW5pbmc/LnByb2NlZHVyZUZpZWxkcz8ubm9kZXMuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAocHJvY2VkdXJlRmllbGQ6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb2NlZHVyZUZpZWxkLmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZElEICE9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS5jdXN0b21pc2VkQ29tcG9uZW50RmllbGRJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgXSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAndXBkYXRlQ3VzdG9taXNlZENvbXBvbmVudEZpZWxkRGF0YSBlcnJvcicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3VwZGF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGEgZXJyb3InLCBlcnJvcilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgKVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVNhdmUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgbGV0IGhhc0Vycm9ycyA9IGZhbHNlXHJcbiAgICAgICAgbGV0IGVycm9ycyA9IHtcclxuICAgICAgICAgICAgVHJhaW5pbmdUeXBlczogJycsXHJcbiAgICAgICAgICAgIFRyYWluZXJJRDogJycsXHJcbiAgICAgICAgICAgIFZlc3NlbElEOiAnJyxcclxuICAgICAgICAgICAgRGF0ZTogJycsXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNldEZvcm1FcnJvcnMoZXJyb3JzKVxyXG4gICAgICAgIGlmIChpc0VtcHR5KHRyYWluaW5nLlRyYWluaW5nVHlwZXMpKSB7XHJcbiAgICAgICAgICAgIGhhc0Vycm9ycyA9IHRydWVcclxuICAgICAgICAgICAgZXJyb3JzLlRyYWluaW5nVHlwZXMgPSAnTmF0dXJlIG9mIHRyYWluaW5nIGlzIHJlcXVpcmVkJ1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoISh0cmFpbmluZy5UcmFpbmVySUQgJiYgdHJhaW5pbmcuVHJhaW5lcklEID4gMCkpIHtcclxuICAgICAgICAgICAgaGFzRXJyb3JzID0gdHJ1ZVxyXG4gICAgICAgICAgICBlcnJvcnMuVHJhaW5lcklEID0gJ1RyYWluZXIgaXMgcmVxdWlyZWQnXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChcclxuICAgICAgICAgICAgIXRyYWluaW5nLlZlc3NlbElEICYmXHJcbiAgICAgICAgICAgICEodHJhaW5pbmcuVHJhaW5pbmdMb2NhdGlvbklEICYmIHRyYWluaW5nLlRyYWluaW5nTG9jYXRpb25JRCA+PSAwKVxyXG4gICAgICAgICkge1xyXG4gICAgICAgICAgICBoYXNFcnJvcnMgPSB0cnVlXHJcbiAgICAgICAgICAgIGVycm9ycy5WZXNzZWxJRCA9ICdMb2NhdGlvbiBpcyByZXF1aXJlZCdcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmICh0eXBlb2YgdHJhaW5pbmcuRGF0ZSA9PT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICAgICAgdHJhaW5pbmcuRGF0ZSA9IGRheWpzKCkuZm9ybWF0KCdZWVlZLU1NLUREJylcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmICh0cmFpbmluZy5EYXRlID09PSBudWxsIHx8ICFkYXlqcyh0cmFpbmluZy5EYXRlKS5pc1ZhbGlkKCkpIHtcclxuICAgICAgICAgICAgaGFzRXJyb3JzID0gdHJ1ZVxyXG4gICAgICAgICAgICBlcnJvcnMuRGF0ZSA9ICdUaGUgZGF0ZSBpcyBpbnZhbGlkJ1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoaGFzRXJyb3JzKSB7XHJcbiAgICAgICAgICAgIHNldEhhc0Zvcm1FcnJvcnModHJ1ZSlcclxuICAgICAgICAgICAgc2V0Rm9ybUVycm9ycyhlcnJvcnMpXHJcbiAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb246XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JzLlRyYWluaW5nVHlwZXMgfHxcclxuICAgICAgICAgICAgICAgICAgICBlcnJvcnMuVHJhaW5lcklEIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JzLlZlc3NlbElEIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JzLkRhdGUsXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnLFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgaW5wdXQgPSB7XHJcbiAgICAgICAgICAgIGlkOiB0cmFpbmluZ0lELFxyXG4gICAgICAgICAgICBkYXRlOiB0cmFpbmluZy5EYXRlXHJcbiAgICAgICAgICAgICAgICA/IGRheWpzKHRyYWluaW5nLkRhdGUpLmZvcm1hdCgnWVlZWS1NTS1ERCcpXHJcbiAgICAgICAgICAgICAgICA6ICcnLFxyXG4gICAgICAgICAgICBtZW1iZXJzOiB0cmFpbmluZy5NZW1iZXJzPy5qb2luKCcsJyksXHJcbiAgICAgICAgICAgIHRyYWluZXJJRDogdHJhaW5pbmcuVHJhaW5lcklELFxyXG4gICAgICAgICAgICB0cmFpbmluZ1N1bW1hcnk6IGNvbnRlbnQsXHJcbiAgICAgICAgICAgIHRyYWluaW5nVHlwZXM6IHRyYWluaW5nLlRyYWluaW5nVHlwZXM/LmpvaW4oJywnKSxcclxuICAgICAgICAgICAgdmVzc2VsSUQ6IHRyYWluaW5nPy5WZXNzZWxJRCxcclxuICAgICAgICAgICAgdHJhaW5pbmdMb2NhdGlvblR5cGU6IHRyYWluaW5nPy5WZXNzZWxJRFxyXG4gICAgICAgICAgICAgICAgPyB0cmFpbmluZy5WZXNzZWxJRCA9PT0gJ090aGVyJyB8fFxyXG4gICAgICAgICAgICAgICAgICB0cmFpbmluZy5WZXNzZWxJRCA9PT0gJ09uc2hvcmUnXHJcbiAgICAgICAgICAgICAgICAgICAgPyB0cmFpbmluZy5WZXNzZWxJRFxyXG4gICAgICAgICAgICAgICAgICAgIDogJ1Zlc3NlbCdcclxuICAgICAgICAgICAgICAgIDogJ0xvY2F0aW9uJyxcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHRyYWluaW5nSUQgPT09IDApIHtcclxuICAgICAgICAgICAgYXdhaXQgbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb24oe1xyXG4gICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IGlucHV0LFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBhd2FpdCBtdXRhdGlvblVwZGF0ZVRyYWluaW5nU2Vzc2lvbih7XHJcbiAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICBpbnB1dDogaW5wdXQsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtcclxuICAgICAgICB0cmFpbmluZyxcclxuICAgICAgICB0cmFpbmluZ0lELFxyXG4gICAgICAgIGNvbnRlbnQsXHJcbiAgICAgICAgYnVmZmVyUHJvY2VkdXJlQ2hlY2ssXHJcbiAgICAgICAgYnVmZmVyRmllbGRDb21tZW50LFxyXG4gICAgICAgIHJhd1RyYWluaW5nLFxyXG4gICAgICAgIG1lbWJlcklkLFxyXG4gICAgICAgIHZlc3NlbElkLFxyXG4gICAgICAgIG11dGF0aW9uQ3JlYXRlVHJhaW5pbmdTZXNzaW9uLFxyXG4gICAgICAgIG11dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uLFxyXG4gICAgICAgIHJvdXRlcixcclxuICAgICAgICB0b2FzdCxcclxuICAgIF0pXHJcbiAgICAvLyB2YXIgc2lnbmF0dXJlQ291bnQgPSAwXHJcblxyXG4gICAgY29uc3QgdXBkYXRlU2lnbmF0dXJlcyA9IChUcmFpbmluZ0lEOiBudW1iZXIpID0+IHtcclxuICAgICAgICBzaWduYXR1cmVNZW1iZXJzLmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgc2lnbmF0dXJlTWVtYmVycz8uZm9yRWFjaCgoc2lnbmF0dXJlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNoZWNrQW5kU2F2ZVNpZ25hdHVyZShzaWduYXR1cmUsIFRyYWluaW5nSUQpXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY2hlY2tBbmRTYXZlU2lnbmF0dXJlID0gYXN5bmMgKFxyXG4gICAgICAgIHNpZ25hdHVyZTogYW55LFxyXG4gICAgICAgIFRyYWluaW5nSUQ6IG51bWJlcixcclxuICAgICkgPT4ge1xyXG4gICAgICAgIGF3YWl0IHF1ZXJ5R2V0TWVtYmVyVHJhaW5pbmdTaWduYXR1cmVzKHtcclxuICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICBmaWx0ZXI6IHtcclxuICAgICAgICAgICAgICAgICAgICBtZW1iZXJJRDogeyBlcTogc2lnbmF0dXJlLk1lbWJlcklEIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uSUQ6IHsgaW46IFRyYWluaW5nSUQgfSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSlcclxuICAgICAgICAgICAgLnRoZW4oKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5kYXRhLnJlYWRNZW1iZXJUcmFpbmluZ19TaWduYXR1cmVzLm5vZGVzXHJcbiAgICAgICAgICAgICAgICBpZiAoZGF0YS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgbXV0YXRpb25VcGRhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogZGF0YVswXS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJJRDogc2lnbmF0dXJlLk1lbWJlcklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpZ25hdHVyZURhdGE6IHNpZ25hdHVyZS5TaWduYXR1cmVEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbklEOiBUcmFpbmluZ0lELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBpZiAoc2lnbmF0dXJlLlNpZ25hdHVyZURhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbXV0YXRpb25DcmVhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJJRDogc2lnbmF0dXJlLk1lbWJlcklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaWduYXR1cmVEYXRhOiBzaWduYXR1cmUuU2lnbmF0dXJlRGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uSUQ6IFRyYWluaW5nSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAuY2F0Y2goKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXHJcbiAgICAgICAgICAgICAgICAgICAgJ211dGF0aW9uR2V0TWVtYmVyVHJhaW5pbmdTaWduYXR1cmVzIGVycm9yJyxcclxuICAgICAgICAgICAgICAgICAgICBlcnJvcixcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbcXVlcnlHZXRNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZXNdID0gdXNlTGF6eVF1ZXJ5KFxyXG4gICAgICAgIEdFVF9NRU1CRVJfVFJBSU5JTkdfU0lHTkFUVVJFUyxcclxuICAgIClcclxuXHJcbiAgICBjb25zdCBbXHJcbiAgICAgICAgbXV0YXRpb25VcGRhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZSxcclxuICAgICAgICB7IGxvYWRpbmc6IG11dGF0aW9uVXBkYXRlTWVtYmVyVHJhaW5pbmdTaWduYXR1cmVMb2FkaW5nIH0sXHJcbiAgICBdID0gdXNlTXV0YXRpb24oVVBEQVRFX01FTUJFUl9UUkFJTklOR19TSUdOQVRVUkUsIHtcclxuICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnVwZGF0ZU1lbWJlclRyYWluaW5nX1NpZ25hdHVyZVxyXG4gICAgICAgICAgICBpZiAoZGF0YS5pZCA+IDApIHtcclxuICAgICAgICAgICAgICAgIC8vIHNpZ25hdHVyZUNvdW50KytcclxuICAgICAgICAgICAgICAgIC8vIGlmIChzaWduYXR1cmVDb3VudCA9PT0gc2lnbmF0dXJlTWVtYmVycy5sZW5ndGgpIHtcclxuICAgICAgICAgICAgICAgIC8vIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXHJcbiAgICAgICAgICAgICAgICAgICAgJ211dGF0aW9uVXBkYXRlTWVtYmVyVHJhaW5pbmdTaWduYXR1cmUgZXJyb3InLFxyXG4gICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdtdXRhdGlvblVwZGF0ZU1lbWJlclRyYWluaW5nU2lnbmF0dXJlIGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcblxyXG4gICAgY29uc3QgW1xyXG4gICAgICAgIG11dGF0aW9uQ3JlYXRlTWVtYmVyVHJhaW5pbmdTaWduYXR1cmUsXHJcbiAgICAgICAgeyBsb2FkaW5nOiBtdXRhdGlvbkNyZWF0ZU1lbWJlclRyYWluaW5nU2lnbmF0dXJlTG9hZGluZyB9LFxyXG4gICAgXSA9IHVzZU11dGF0aW9uKENSRUFURV9NRU1CRVJfVFJBSU5JTkdfU0lHTkFUVVJFLCB7XHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5jcmVhdGVNZW1iZXJUcmFpbmluZ19TaWduYXR1cmVcclxuICAgICAgICAgICAgaWYgKGRhdGEuaWQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBzaWduYXR1cmVDb3VudCsrXHJcbiAgICAgICAgICAgICAgICAvLyBpZiAoc2lnbmF0dXJlQ291bnQgPT09IHNpZ25hdHVyZU1lbWJlcnMubGVuZ3RoKSB7XHJcbiAgICAgICAgICAgICAgICAvLyB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFxyXG4gICAgICAgICAgICAgICAgICAgICdtdXRhdGlvbkNyZWF0ZU1lbWJlclRyYWluaW5nU2lnbmF0dXJlIGVycm9yJyxcclxuICAgICAgICAgICAgICAgICAgICByZXNwb25zZSxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignbXV0YXRpb25DcmVhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZSBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVRyYWluaW5nRGF0ZUNoYW5nZSA9IHVzZUNhbGxiYWNrKChkYXRlOiBhbnkpID0+IHtcclxuICAgICAgICBzZXRUcmFpbmluZ0RhdGUoZGF0ZSAmJiBuZXcgRGF0ZShkYXRlLnRvU3RyaW5nKCkpKVxyXG4gICAgICAgIHNldFRyYWluaW5nKChwcmV2VHJhaW5pbmc6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgLi4ucHJldlRyYWluaW5nLFxyXG4gICAgICAgICAgICBEYXRlOiBkYXlqcyhkYXRlKS5mb3JtYXQoJ1lZWVktTU0tREQnKSxcclxuICAgICAgICB9KSlcclxuICAgIH0sIFtdKVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVRyYWluZXJDaGFuZ2UgPSB1c2VDYWxsYmFjaygodHJhaW5lcjogYW55KSA9PiB7XHJcbiAgICAgICAgaWYgKCF0cmFpbmVyKSByZXR1cm4gLy8gQWRkIGVhcmx5IHJldHVybiBpZiB0cmFpbmVyIGlzIG51bGxcclxuXHJcbiAgICAgICAgLy8gVXNlIFNldCgpIHRvIHByZXZlbnQgZHVwbGljYXRlIHZhbHVlcywgdGhlbiBBcnJheS5mcm9tKCkgdG8gY29udmVydCBpdCB0byBhbiBhcnJheVxyXG4gICAgICAgIHNldFRyYWluaW5nKChwcmV2VHJhaW5pbmc6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBtZW1iZXJzU2V0ID0gbmV3IFNldChwcmV2VHJhaW5pbmc/Lk1lbWJlcnMgfHwgW10pXHJcbiAgICAgICAgICAgIG1lbWJlcnNTZXQuYWRkKHRyYWluZXIudmFsdWUpXHJcbiAgICAgICAgICAgIGNvbnN0IG1lbWJlcnMgPSBBcnJheS5mcm9tKG1lbWJlcnNTZXQpXHJcbiAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAuLi5wcmV2VHJhaW5pbmcsXHJcbiAgICAgICAgICAgICAgICBUcmFpbmVySUQ6IHRyYWluZXIudmFsdWUsXHJcbiAgICAgICAgICAgICAgICBNZW1iZXJzOiBtZW1iZXJzLFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgICAgICBzZXRTZWxlY3RlZE1lbWJlckxpc3QoKHByZXZMaXN0OiBhbnkpID0+IFsuLi5wcmV2TGlzdCwgdHJhaW5lcl0pXHJcbiAgICAgICAgc2V0U2lnbmF0dXJlTWVtYmVycygocHJldlNpZ25hdHVyZXM6IGFueSkgPT4gW1xyXG4gICAgICAgICAgICAuLi5wcmV2U2lnbmF0dXJlcyxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgTWVtYmVySUQ6ICt0cmFpbmVyLnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgU2lnbmF0dXJlRGF0YTogbnVsbCxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICBdKVxyXG4gICAgfSwgW10pXHJcblxyXG4gICAgY29uc3QgaGFuZGxlVHJhaW5pbmdUeXBlQ2hhbmdlID0gdXNlQ2FsbGJhY2soKHRyYWluaW5nVHlwZXM6IGFueSkgPT4ge1xyXG4gICAgICAgIHNldFRyYWluaW5nKChwcmV2VHJhaW5pbmc6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgLi4ucHJldlRyYWluaW5nLFxyXG4gICAgICAgICAgICBUcmFpbmluZ1R5cGVzOiB0cmFpbmluZ1R5cGVzLm1hcCgoaXRlbTogYW55KSA9PiBpdGVtLnZhbHVlKSxcclxuICAgICAgICB9KSlcclxuICAgIH0sIFtdKVxyXG4gICAgLyogY29uc3QgaGFuZGxlVHJhaW5pbmdMb2NhdGlvbkNoYW5nZSA9ICh2ZXNzZWw6IGFueSkgPT4ge1xyXG4gICAgICAgIHNldFRyYWluaW5nKHtcclxuICAgICAgICAgICAgLi4udHJhaW5pbmcsXHJcbiAgICAgICAgICAgIFZlc3NlbElEOiB2ZXNzZWwuaXNWZXNzZWwgPyB2ZXNzZWwudmFsdWUgOiAwLFxyXG4gICAgICAgICAgICBUcmFpbmluZ0xvY2F0aW9uSUQ6ICF2ZXNzZWwuaXNWZXNzZWwgPyB2ZXNzZWwudmFsdWUgOiAwLFxyXG4gICAgICAgIH0pXHJcbiAgICB9ICovXHJcbiAgICBjb25zdCBoYW5kbGVNZW1iZXJDaGFuZ2UgPSB1c2VDYWxsYmFjaygobWVtYmVyczogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ/CflKcgVHJhaW5pbmdGb3JtIC0gaGFuZGxlTWVtYmVyQ2hhbmdlIGNhbGxlZCB3aXRoOicsIHtcclxuICAgICAgICAgICAgbWVtYmVycyxcclxuICAgICAgICAgICAgbWVtYmVyc0xlbmd0aDogbWVtYmVycz8ubGVuZ3RoLFxyXG4gICAgICAgICAgICBtZW1iZXJzVHlwZTogdHlwZW9mIG1lbWJlcnMsXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgY29uc3QgbWVtYmVySWRzID0gbWVtYmVycy5tYXAoKGl0ZW06IGFueSkgPT4gaXRlbS52YWx1ZSlcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UpyBUcmFpbmluZ0Zvcm0gLSBleHRyYWN0ZWQgbWVtYmVyIElEczonLCBtZW1iZXJJZHMpXHJcblxyXG4gICAgICAgIHNldFRyYWluaW5nKChwcmV2VHJhaW5pbmc6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgLi4ucHJldlRyYWluaW5nLFxyXG4gICAgICAgICAgICBNZW1iZXJzOiBtZW1iZXJJZHMsXHJcbiAgICAgICAgfSkpXHJcbiAgICAgICAgc2V0U2VsZWN0ZWRNZW1iZXJMaXN0KG1lbWJlcnMpXHJcbiAgICAgICAgc2V0U2lnbmF0dXJlTWVtYmVycygocHJldlNpZ25hdHVyZXM6IGFueSkgPT5cclxuICAgICAgICAgICAgcHJldlNpZ25hdHVyZXMuZmlsdGVyKChpdGVtOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICBtZW1iZXJzLnNvbWUoKG06IGFueSkgPT4gK20udmFsdWUgPT09IGl0ZW0uTWVtYmVySUQpLFxyXG4gICAgICAgICAgICApLFxyXG4gICAgICAgIClcclxuICAgIH0sIFtdKVxyXG4gICAgY29uc3Qgb25TaWduYXR1cmVDaGFuZ2VkID0gKFxyXG4gICAgICAgIGU6IHN0cmluZyxcclxuICAgICAgICBtZW1iZXI6IHN0cmluZyxcclxuICAgICAgICBtZW1iZXJJZDogbnVtYmVyLFxyXG4gICAgKSA9PiB7XHJcbiAgICAgICAgY29uc3QgaW5kZXggPSBzaWduYXR1cmVNZW1iZXJzLmZpbmRJbmRleChcclxuICAgICAgICAgICAgKG9iamVjdCkgPT4gb2JqZWN0Lk1lbWJlcklEID09PSBtZW1iZXJJZCxcclxuICAgICAgICApXHJcbiAgICAgICAgY29uc3QgdXBkYXRlZE1lbWJlcnMgPSBbLi4uc2lnbmF0dXJlTWVtYmVyc11cclxuICAgICAgICBpZiAoZSkge1xyXG4gICAgICAgICAgICBpZiAoaW5kZXggIT09IC0xKSB7XHJcbiAgICAgICAgICAgICAgICBpZiAoZS50cmltKCkgPT09ICcnKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgdXBkYXRlZE1lbWJlcnMuc3BsaWNlKGluZGV4LCAxKVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICB1cGRhdGVkTWVtYmVyc1tpbmRleF0uU2lnbmF0dXJlRGF0YSA9IGVcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHVwZGF0ZWRNZW1iZXJzLnB1c2goeyBNZW1iZXJJRDogbWVtYmVySWQsIFNpZ25hdHVyZURhdGE6IGUgfSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHVwZGF0ZWRNZW1iZXJzLnNwbGljZShpbmRleCwgMSlcclxuICAgICAgICB9XHJcbiAgICAgICAgc2V0U2lnbmF0dXJlTWVtYmVycyh1cGRhdGVkTWVtYmVycylcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVUcmFpbmluZ1Zlc3NlbENoYW5nZSA9IHVzZUNhbGxiYWNrKFxyXG4gICAgICAgICh2ZXNzZWw6IE9wdGlvbiB8IE9wdGlvbltdIHwgbnVsbCkgPT4ge1xyXG4gICAgICAgICAgICBzZXRUcmFpbmluZygocHJldlRyYWluaW5nOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAuLi5wcmV2VHJhaW5pbmcsXHJcbiAgICAgICAgICAgICAgICBWZXNzZWxJRDogdmVzc2VsXHJcbiAgICAgICAgICAgICAgICAgICAgPyB0eXBlb2YgdmVzc2VsID09PSAnb2JqZWN0JyAmJiAhQXJyYXkuaXNBcnJheSh2ZXNzZWwpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gdmVzc2VsLnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogMFxyXG4gICAgICAgICAgICAgICAgICAgIDogMCxcclxuICAgICAgICAgICAgfSkpXHJcbiAgICAgICAgfSxcclxuICAgICAgICBbXSxcclxuICAgIClcclxuXHJcbiAgICBjb25zdCBoYW5kbGVFZGl0b3JDaGFuZ2UgPSB1c2VDYWxsYmFjaygobmV3Q29udGVudDogYW55KSA9PiB7XHJcbiAgICAgICAgc2V0Q29udGVudChuZXdDb250ZW50KVxyXG4gICAgfSwgW10pXHJcblxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAoIWlzRW1wdHkodHJhaW5pbmcpKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHZpZCA9XHJcbiAgICAgICAgICAgICAgICB2ZXNzZWxJZCA+IDAgfHwgaXNOYU4ocGFyc2VJbnQodHJhaW5pbmc/LlZlc3NlbElELCAxMCkpXHJcbiAgICAgICAgICAgICAgICAgICAgPyB2ZXNzZWxJZFxyXG4gICAgICAgICAgICAgICAgICAgIDogcGFyc2VJbnQodHJhaW5pbmc/LlZlc3NlbElELCAxMClcclxuICAgICAgICAgICAgc2V0VmVzc2VsSUQodmlkKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFt2ZXNzZWxJZCwgdHJhaW5pbmddKVxyXG5cclxuICAgIGNvbnN0IFtwZXJtaXNzaW9ucywgc2V0UGVybWlzc2lvbnNdID0gdXNlU3RhdGU8YW55PihmYWxzZSlcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIHNldFBlcm1pc3Npb25zKGdldFBlcm1pc3Npb25zKVxyXG4gICAgfSwgW10pXHJcblxyXG4gICAgaWYgKFxyXG4gICAgICAgICFwZXJtaXNzaW9ucyB8fFxyXG4gICAgICAgICghaGFzUGVybWlzc2lvbignRURJVF9UUkFJTklORycsIHBlcm1pc3Npb25zKSAmJlxyXG4gICAgICAgICAgICAhaGFzUGVybWlzc2lvbignVklFV19UUkFJTklORycsIHBlcm1pc3Npb25zKSAmJlxyXG4gICAgICAgICAgICAhaGFzUGVybWlzc2lvbignUkVDT1JEX1RSQUlOSU5HJywgcGVybWlzc2lvbnMpKVxyXG4gICAgKSB7XHJcbiAgICAgICAgcmV0dXJuICFwZXJtaXNzaW9ucyA/IChcclxuICAgICAgICAgICAgPExvYWRpbmcgLz5cclxuICAgICAgICApIDogKFxyXG4gICAgICAgICAgICA8TG9hZGluZyBlcnJvck1lc3NhZ2U9XCJPb3BzIFlvdSBkbyBub3QgaGF2ZSB0aGUgcGVybWlzc2lvbiB0byB2aWV3IHRoaXMgc2VjdGlvbi5cIiAvPlxyXG4gICAgICAgIClcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBnZXRQcm9jZWR1cmVzID0gKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IHByb2NlZHVyZXMgPSB0cmFpbmluZ1R5cGVzLmZpbHRlcigodHlwZTogYW55KSA9PlxyXG4gICAgICAgICAgICB0cmFpbmluZz8uVHJhaW5pbmdUeXBlcz8uaW5jbHVkZXModHlwZS5pZCksXHJcbiAgICAgICAgKVxyXG4gICAgICAgIHJldHVybiBwcm9jZWR1cmVzXHJcbiAgICAgICAgICAgIC5tYXAoKHR5cGU6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHR5cGUuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkLm5vZGVzLmxlbmd0aCA+IDBcclxuICAgICAgICAgICAgICAgICAgICA/IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogdHlwZS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogdHlwZS50aXRsZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZHM6IFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udHlwZS5jdXN0b21pc2VkQ29tcG9uZW50RmllbGQubm9kZXMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgXT8uc29ydChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGE6IGFueSwgYjogYW55KSA9PiBhLnNvcnRPcmRlciAtIGIuc29ydE9yZGVyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgOiBudWxsXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIC5maWx0ZXIoKHR5cGU6IGFueSkgPT4gdHlwZSAhPSBudWxsKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGNyZXdWYWx1ZSA9IHVzZU1lbW8oKCkgPT4ge1xyXG4gICAgICAgIHJldHVybiBtZW1iZXJJZCA+IDAgPyBbbWVtYmVySWQudG9TdHJpbmcoKV0gOiB0cmFpbmluZz8uTWVtYmVyc1xyXG4gICAgfSwgW21lbWJlcklkLCB0cmFpbmluZz8uTWVtYmVyc10pXHJcblxyXG4gICAgY29uc3QgaGFzVHJhaW5pbmdQcm9jZWR1cmVzID0gdXNlTWVtbygoKSA9PiB7XHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgdHJhaW5pbmcgJiZcclxuICAgICAgICAgICAgdHJhaW5pbmdUeXBlcy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAodHlwZTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgIHRyYWluaW5nPy5UcmFpbmluZ1R5cGVzPy5pbmNsdWRlcyh0eXBlLmlkKSAmJlxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGUucHJvY2VkdXJlLFxyXG4gICAgICAgICAgICApLmxlbmd0aCA+IDBcclxuICAgICAgICApXHJcbiAgICB9LCBbdHJhaW5pbmcsIHRyYWluaW5nVHlwZXNdKVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVByb2NlZHVyZUNoZWNrcyA9IChmaWVsZDogYW55LCB0eXBlOiBhbnksIHN0YXR1czogYm9vbGVhbikgPT4ge1xyXG4gICAgICAgIC8vIEFsd2F5cyB1c2UgYnVmZmVyIHN5c3RlbSBmb3IgY29uc2lzdGVuY3ksIHdoZXRoZXIgY3JlYXRpbmcgb3IgdXBkYXRpbmdcclxuICAgICAgICBjb25zdCBwcm9jZWR1cmVDaGVjayA9IGJ1ZmZlclByb2NlZHVyZUNoZWNrLmZpbHRlcihcclxuICAgICAgICAgICAgKHByb2NlZHVyZUZpZWxkOiBhbnkpID0+IHByb2NlZHVyZUZpZWxkLmZpZWxkSWQgIT09IGZpZWxkLmlkLFxyXG4gICAgICAgIClcclxuICAgICAgICBzZXRCdWZmZXJQcm9jZWR1cmVDaGVjayhbXHJcbiAgICAgICAgICAgIC4uLnByb2NlZHVyZUNoZWNrLFxyXG4gICAgICAgICAgICB7IGZpZWxkSWQ6IGZpZWxkLmlkLCBzdGF0dXM6IHN0YXR1cyB9LFxyXG4gICAgICAgIF0pXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZ2V0RmllbGRTdGF0dXMgPSAoZmllbGQ6IGFueSkgPT4ge1xyXG4gICAgICAgIGlmIChidWZmZXJQcm9jZWR1cmVDaGVjay5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkU3RhdHVzID0gYnVmZmVyUHJvY2VkdXJlQ2hlY2suZmluZChcclxuICAgICAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PiBwcm9jZWR1cmVGaWVsZC5maWVsZElkID09IGZpZWxkLmlkLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIGlmIChmaWVsZFN0YXR1cykge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZpZWxkU3RhdHVzLnN0YXR1cyA/ICdPaycgOiAnTm90X09rJ1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IGZpZWxkU3RhdHVzID0gcmF3VHJhaW5pbmc/LnByb2NlZHVyZUZpZWxkcz8ubm9kZXM/LmZpbmQoXHJcbiAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkSUQgPT0gZmllbGQuaWQsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIHJldHVybiBmaWVsZFN0YXR1cz8uc3RhdHVzIHx8ICcnXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3Qgc2hvd0NvbW1lbnRQb3B1cCA9IChmaWVsZDogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc3QgZmllbGRDb21tZW50ID0gcmF3VHJhaW5pbmc/LnByb2NlZHVyZUZpZWxkcz8ubm9kZXM/LmZpbmQoXHJcbiAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkSUQgPT0gZmllbGQuaWQsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIGlmIChidWZmZXJGaWVsZENvbW1lbnQubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICBjb25zdCBmaWVsZENvbW1lbnQgPSBidWZmZXJGaWVsZENvbW1lbnQuZmluZChcclxuICAgICAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PiBwcm9jZWR1cmVGaWVsZC5maWVsZElkID09IGZpZWxkLmlkLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIHNldEN1cnJlbnRDb21tZW50KGZpZWxkQ29tbWVudD8uY29tbWVudCB8fCAnJylcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBzZXRDdXJyZW50Q29tbWVudChmaWVsZENvbW1lbnQ/LmNvbW1lbnQgfHwgJycpXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNldEN1cnJlbnRGaWVsZChmaWVsZClcclxuICAgICAgICBzZXRPcGVuQ29tbWVudEFsZXJ0KHRydWUpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZ2V0Q29tbWVudCA9IChmaWVsZDogYW55KSA9PiB7XHJcbiAgICAgICAgaWYgKGJ1ZmZlckZpZWxkQ29tbWVudC5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkQ29tbWVudCA9IGJ1ZmZlckZpZWxkQ29tbWVudC5maW5kKFxyXG4gICAgICAgICAgICAgICAgKHByb2NlZHVyZUZpZWxkOiBhbnkpID0+IHByb2NlZHVyZUZpZWxkLmZpZWxkSWQgPT0gZmllbGQuaWQsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgICAgaWYgKGZpZWxkQ29tbWVudCkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZpZWxkQ29tbWVudC5jb21tZW50XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgZmllbGRDb21tZW50ID0gcmF3VHJhaW5pbmc/LnByb2NlZHVyZUZpZWxkcz8ubm9kZXM/LmZpbmQoXHJcbiAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkSUQgPT0gZmllbGQuaWQsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIHJldHVybiBmaWVsZENvbW1lbnQ/LmNvbW1lbnQgfHwgZmllbGQuY29tbWVudFxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVNhdmVDb21tZW50ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgICAgIC8vIEFsd2F5cyB1c2UgYnVmZmVyIHN5c3RlbSBmb3IgY29uc2lzdGVuY3ksIHdoZXRoZXIgY3JlYXRpbmcgb3IgdXBkYXRpbmdcclxuICAgICAgICBjb25zdCBmaWVsZENvbW1lbnQgPSBidWZmZXJGaWVsZENvbW1lbnQuZmlsdGVyKFxyXG4gICAgICAgICAgICAocHJvY2VkdXJlRmllbGQ6IGFueSkgPT4gcHJvY2VkdXJlRmllbGQuZmllbGRJZCAhPT0gY3VycmVudEZpZWxkLmlkLFxyXG4gICAgICAgIClcclxuICAgICAgICBzZXRCdWZmZXJGaWVsZENvbW1lbnQoW1xyXG4gICAgICAgICAgICAuLi5maWVsZENvbW1lbnQsXHJcbiAgICAgICAgICAgIHsgZmllbGRJZDogY3VycmVudEZpZWxkLmlkLCBjb21tZW50OiBjdXJyZW50Q29tbWVudCB9LFxyXG4gICAgICAgIF0pXHJcbiAgICAgICAgc2V0T3BlbkNvbW1lbnRBbGVydChmYWxzZSlcclxuICAgIH0sIFtidWZmZXJGaWVsZENvbW1lbnQsIGN1cnJlbnRGaWVsZCwgY3VycmVudENvbW1lbnRdKVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVZpZXdQcm9jZWR1cmVzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgICAgIHNldE9wZW5WaWV3UHJvY2VkdXJlKHRydWUpXHJcbiAgICB9LCBbXSlcclxuXHJcbiAgICBjb25zdCBoYW5kbGVDYW5jZWxDbGljayA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgICAgICByb3V0ZXIucHVzaCgnL2NyZXctdHJhaW5pbmcnKVxyXG4gICAgfSwgW3JvdXRlcl0pXHJcblxyXG4gICAgY29uc3QgaGFuZGxlQ29tbWVudENoYW5nZSA9IHVzZUNhbGxiYWNrKChlOiBhbnkpID0+IHtcclxuICAgICAgICBzZXRDdXJyZW50Q29tbWVudChlLnRhcmdldC52YWx1ZSlcclxuICAgIH0sIFtdKVxyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwibWItMi41IG14LTRcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGU+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHt0cmFpbmluZ0lEID09PSAwID8gJ05ldycgOiAnRWRpdCd9IFRyYWluaW5nIFNlc3Npb25cclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cclxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgIDxTZXBhcmF0b3IgY2xhc3NOYW1lPVwibXktNVwiIC8+XHJcbiAgICAgICAgICAgICAgICB7IXRyYWluaW5nICYmIHRyYWluaW5nSUQgPiAwID8gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxUcmFpbmluZ1Nlc3Npb25Gb3JtU2tlbGV0b24gLz5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMyBtZDpjb2wtc3Bhbi0xIG15LTQgXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVHJhaW5pbmcgRGV0YWlsc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIiBtdC00IG1heC13LVsyNXJlbV0gbGVhZGluZy1sb29zZSBtYi00XCI+PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtoYXNUcmFpbmluZ1Byb2NlZHVyZXMgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZVZpZXdQcm9jZWR1cmVzfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZpZXcgUHJvY2VkdXJlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTMgbWQ6Y29sLXNwYW4tMiBwdC04IHBiLTUgc3BhY2UteS02IHB4LTcgYm9yZGVyIGJvcmRlci1ib3JkZXIgYm9yZGVyLWRhc2hlZCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbXktNCBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgbGFiZWw9XCJUcmFpbmVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENyZXdEcm9wZG93blxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17dHJhaW5pbmc/LlRyYWluZXJJRH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsSUQ9e3Zlc3NlbElEfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlVHJhaW5lckNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzbWFsbCBjbGFzc05hbWU9XCJ0ZXh0LWRlc3RydWN0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2hhc0Zvcm1FcnJvcnMgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybUVycm9ycy5UcmFpbmVySUR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NtYWxsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6bXQtNCBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHJhaW5pbmdUeXBlTXVsdGlTZWxlY3REcm9wZG93blxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0cmFpbmluZz8uVHJhaW5pbmdUeXBlc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlVHJhaW5pbmdUeXBlQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzbWFsbCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC12aXZpZC01MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aGFzRm9ybUVycm9ycyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3JtRXJyb3JzLlRyYWluaW5nVHlwZXN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NtYWxsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBtdC00IGZsZXggZmxleC1jb2xcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPkNyZXc8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q3Jld011bHRpU2VsZWN0RHJvcGRvd25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjcmV3VmFsdWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxJRD17dmVzc2VsSUR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlTWVtYmVyQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZXBhcmF0b3IgY2xhc3NOYW1lPVwibXktNVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHctZnVsbCBnYXAtNCBtdC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIDxEYXRlUmFuZ2VQaWNrZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlVHJhaW5pbmdEYXRlQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPiAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEYXRlUGlja2VyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9kZT1cInNpbmdsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZVRyYWluaW5nRGF0ZUNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3IERhdGUodHJhaW5pbmdEYXRlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c21hbGwgY2xhc3NOYW1lPVwidGV4dC1kZXN0cnVjdGl2ZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtoYXNGb3JtRXJyb3JzICYmIGZvcm1FcnJvcnMuRGF0ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc21hbGw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Zlc3NlbHMgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDb21ib2JveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXt2ZXNzZWxzLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh2ZXNzZWw6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogdmVzc2VsLmxhYmVsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiB2ZXNzZWwudmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlcz17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByYXdUcmFpbmluZz8udHJhaW5pbmdMb2NhdGlvblR5cGUgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnVmVzc2VsJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcmF3VHJhaW5pbmc/LlZlc3NlbElEPy50b1N0cmluZygpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiByYXdUcmFpbmluZz8udHJhaW5pbmdMb2NhdGlvblR5cGUgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdPbnNob3JlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnRGVza3RvcC9zaG9yZSdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcmF3VHJhaW5pbmc/LnRyYWluaW5nTG9jYXRpb25UeXBlID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdPdGhlcidcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnT3RoZXInXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcmF3VHJhaW5pbmc/LnRyYWluaW5nTG9jYXRpb25UeXBlID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdMb2NhdGlvbicgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJhd1RyYWluaW5nXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPy50cmFpbmluZ0xvY2F0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPy5pZCA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHJhd1RyYWluaW5nPy50cmFpbmluZ0xvY2F0aW9uPy5pZC50b1N0cmluZygpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB2ZXNzZWxJZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogdmVzc2Vscy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsOiBhbnksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWwudmFsdWUgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbElkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKT8ubGFiZWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogdmVzc2VsSWQudG9TdHJpbmcoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBudWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNMb2FkaW5nPXtyYXdUcmFpbmluZ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlVHJhaW5pbmdWZXNzZWxDaGFuZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlbGVjdCBsb2NhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNtYWxsIGNsYXNzTmFtZT1cInRleHQtZGVzdHJ1Y3RpdmVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aGFzRm9ybUVycm9ycyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3JtRXJyb3JzLlZlc3NlbElEfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zbWFsbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHJhaW5pbmdMb2NhdGlvbkRyb3Bkb3duXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17dHJhaW5pbmc/LlRyYWluaW5nTG9jYXRpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlVHJhaW5pbmdMb2NhdGlvbkNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNtYWxsIGNsYXNzTmFtZT1cInRleHQtZGVzdHJ1Y3RpdmVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtoYXNGb3JtRXJyb3JzICYmIGZvcm1FcnJvcnMuVmVzc2VsSUR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc21hbGw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+ICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMyBtZDpjb2wtc3Bhbi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRQcm9jZWR1cmVzKCkubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRQcm9jZWR1cmVzKCkubWFwKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAodHlwZTogYW55KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tGaWVsZCBrZXk9e3R5cGUuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD17dHlwZS50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0ZpZWxkQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3R5cGUuZmllbGRzLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZDogYW55LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPERhaWx5Q2hlY2tGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGQuaWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5RmllbGQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGQuc3RhdHVzID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1JlcXVpcmVkJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXlEZXNjcmlwdGlvbj17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZC5kZXNjcmlwdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXlMYWJlbD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZC5maWVsZE5hbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dElkPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkLmlkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlTm9DaGFuZ2U9eygpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVQcm9jZWR1cmVDaGVja3MoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYWxzZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0Tm9DaGVja2VkPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdldEZpZWxkU3RhdHVzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdOb3RfT2snXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlWWVzQ2hhbmdlPXsoKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlUHJvY2VkdXJlQ2hlY2tzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0WWVzQ2hlY2tlZD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZXRGaWVsZFN0YXR1cyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnT2snXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29tbWVudEFjdGlvbj17KCkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dDb21tZW50UG9wdXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29tbWVudD17Z2V0Q29tbWVudChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5SW1hZ2U9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdJRCA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGRJbWFnZXM9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGRJbWFnZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkltYWdlVXBsb2FkPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlZnJlc2hJbWFnZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWN0aW9uRGF0YT17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHRyYWluaW5nSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWN0aW9uTmFtZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndHJhaW5pbmdTZXNzaW9uSUQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2hlY2tGaWVsZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NoZWNrRmllbGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTQgZmxleCBpdGVtcy1jZW50ZXIgdy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RWRpdG9yXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJUcmFpbmluZ1N1bW1hcnlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU3VtbWFyeSBvZiB0cmFpbmluZywgaWRlbnRpZnkgYW55IG91dGNvbWVzLCBmdXJ0aGVyIHRyYWluaW5nIHJlcXVpcmVkIG9yIG90aGVyIG9ic2VydmF0aW9ucy5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIiF3LWZ1bGwgIHJpbmctMSByaW5nLWluc2V0IFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRWRpdG9yQ2hhbmdlPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRWRpdG9yQ2hhbmdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ9e2NvbnRlbnR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyo8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBteS00IGZsZXggZmxleC1jb2xcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJUcmFpbmluZ1N1bW1hcnlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlN1bW1hcnlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0VmFsdWU9e3RyYWluaW5nPy5UcmFpbmluZ1N1bW1hcnl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Jyd9PjwvdGV4dGFyZWE+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4qL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFNlcGFyYXRvciBjbGFzc05hbWU9XCJteS01XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTMgbWQ6Y29sLXNwYW4tMSBtZDpteS00IFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFNpZ25hdHVyZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0zIHNtOmNvbC1zcGFuLTIgbWQ6bXktNCBmbGV4IGp1c3RpZnktYmV0d2VlbiBmbGV4LXdyYXAgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRNZW1iZXJMaXN0ICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkTWVtYmVyTGlzdC5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAobWVtYmVyOiBhbnksIGluZGV4OiBudW1iZXIpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LTk2XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTaWduYXR1cmVQYWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXI9e21lbWJlci5sYWJlbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcklkPXttZW1iZXIudmFsdWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblNpZ25hdHVyZUNoYW5nZWQ9eyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaWduYXR1cmU6IHN0cmluZyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXI/OiBzdHJpbmcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVySWQ/OiBudW1iZXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25TaWduYXR1cmVDaGFuZ2VkKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaWduYXR1cmUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlciA/PyAnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVySWQgfHwgMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaWduYXR1cmU9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogc2lnbmF0dXJlTWVtYmVycy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoc2lnOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaWcuTWVtYmVySUQgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIudmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKT8uSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICA8Rm9vdGVyV3JhcHBlcj5cclxuICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiYmFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2FuY2VsQ2xpY2t9XHJcbiAgICAgICAgICAgICAgICAgICAgaWNvbkxlZnQ9e0Fycm93TGVmdH0+XHJcbiAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXHJcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgIHsvKiA8QnV0dG9uIGNsYXNzTmFtZT1cImdyb3VwIGlubGluZS1mbGV4IGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBtci04IHJvdW5kZWQtbWQgYmctcm9zZS0xMDAgcHgtMyBweS0yICB0ZXh0LXJvc2UtNjAwIHNoYWRvdy1zbSBob3ZlcjogaG92ZXI6dGV4dC1yb3NlLTYwMCByaW5nLTEgcmluZy1yb3NlLTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlV2lkdGg9XCIxLjVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBjbGFzc05hbWU9XCItbWwtMC41IG1yLTEuNSBoLTUgdy01IGJvcmRlciBib3JkZXItcm9zZS02MDAgZ3JvdXAtaG92ZXI6Ym9yZGVyLXdoaXRlIHJvdW5kZWQtZnVsbCBncm91cC1ob3ZlcjpiZy1yb3NlLTYwMCBncm91cC1ob3ZlcjpcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIGQ9XCJNNiAxOCAxOCA2TTYgNmwxMiAxMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRGVsZXRlIHRhc2tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPiAqL31cclxuICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTYXZlfVxyXG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb25Mb2FkaW5nIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG11dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uTG9hZGluZ1xyXG4gICAgICAgICAgICAgICAgICAgIH0+XHJcbiAgICAgICAgICAgICAgICAgICAge3RyYWluaW5nSUQgPT09IDAgPyAnQ3JlYXRlIHNlc3Npb24nIDogJ1VwZGF0ZSBzZXNzaW9uJ31cclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L0Zvb3RlcldyYXBwZXI+XHJcbiAgICAgICAgICAgIDxTaGVldCBvcGVuPXtvcGVuVmlld1Byb2NlZHVyZX0gb25PcGVuQ2hhbmdlPXtzZXRPcGVuVmlld1Byb2NlZHVyZX0+XHJcbiAgICAgICAgICAgICAgICA8U2hlZXRDb250ZW50IHNpZGU9XCJyaWdodFwiIGNsYXNzTmFtZT1cInctWzQwMHB4XSBzbTp3LVs1NDBweF1cIj5cclxuICAgICAgICAgICAgICAgICAgICA8U2hlZXRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTaGVldFRpdGxlPlByb2NlZHVyZXM8L1NoZWV0VGl0bGU+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9TaGVldEhlYWRlcj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAge3RyYWluaW5nICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nVHlwZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKHR5cGU6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmc/LlRyYWluaW5nVHlwZXM/LmluY2x1ZGVzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSAmJiB0eXBlLnByb2NlZHVyZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoKHR5cGU6IGFueSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt0eXBlLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYi00IHB4LTIuNSBzbTpweC01XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxIND57dHlwZS50aXRsZX08L0g0PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfX2h0bWw6IHR5cGUucHJvY2VkdXJlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgPC9TaGVldENvbnRlbnQ+XHJcbiAgICAgICAgICAgIDwvU2hlZXQ+XHJcbiAgICAgICAgICAgIDxBbGVydERpYWxvZ1xyXG4gICAgICAgICAgICAgICAgb3Blbj17b3BlbkNvbW1lbnRBbGVydH1cclxuICAgICAgICAgICAgICAgIG9uT3BlbkNoYW5nZT17c2V0T3BlbkNvbW1lbnRBbGVydH0+XHJcbiAgICAgICAgICAgICAgICA8QWxlcnREaWFsb2dDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgIDxBbGVydERpYWxvZ0hlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEFsZXJ0RGlhbG9nVGl0bGU+QWRkIENvbW1lbnQ8L0FsZXJ0RGlhbG9nVGl0bGU+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxBbGVydERpYWxvZ0Rlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQWRkIGEgY29tbWVudCBmb3IgdGhpcyBwcm9jZWR1cmUgY2hlY2suXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQWxlcnREaWFsb2dEZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICA8L0FsZXJ0RGlhbG9nSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y3VycmVudENvbW1lbnR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDb21tZW50Q2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgY29tbWVudCBoZXJlLi4uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17NH1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxBbGVydERpYWxvZ0Zvb3Rlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEFsZXJ0RGlhbG9nQ2FuY2VsPkNhbmNlbDwvQWxlcnREaWFsb2dDYW5jZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxBbGVydERpYWxvZ0FjdGlvbiBvbkNsaWNrPXtoYW5kbGVTYXZlQ29tbWVudH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBTYXZlIENvbW1lbnRcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9BbGVydERpYWxvZ0FjdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICA8L0FsZXJ0RGlhbG9nRm9vdGVyPlxyXG4gICAgICAgICAgICAgICAgPC9BbGVydERpYWxvZ0NvbnRlbnQ+XHJcbiAgICAgICAgICAgIDwvQWxlcnREaWFsb2c+XHJcbiAgICAgICAgPC8+XHJcbiAgICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IFRyYWluaW5nRm9ybVxyXG4iXSwibmFtZXMiOlsiZGF5anMiLCJpc0VtcHR5IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsIkNyZXdEcm9wZG93biIsIlRyYWluaW5nU2Vzc2lvbkZvcm1Ta2VsZXRvbiIsIlRyYWluaW5nVHlwZU11bHRpU2VsZWN0RHJvcGRvd24iLCJDcmV3TXVsdGlTZWxlY3REcm9wZG93biIsIkNSRUFURV9UUkFJTklOR19TRVNTSU9OIiwiVVBEQVRFX1RSQUlOSU5HX1NFU1NJT04iLCJDUkVBVEVfTUVNQkVSX1RSQUlOSU5HX1NJR05BVFVSRSIsIlVQREFURV9NRU1CRVJfVFJBSU5JTkdfU0lHTkFUVVJFIiwiQ1JFQVRFX1RSQUlOSU5HX1NFU1NJT05fRFVFIiwiVVBEQVRFX1RSQUlOSU5HX1NFU1NJT05fRFVFIiwiQ1JFQVRFX0NVU1RPTUlTRURfQ09NUE9ORU5UX0ZJRUxEX0RBVEEiLCJVUERBVEVfQ1VTVE9NSVNFRF9DT01QT05FTlRfRklFTERfREFUQSIsIkdFVF9NRU1CRVJfVFJBSU5JTkdfU0lHTkFUVVJFUyIsIkdFVF9TRUNUSU9OX01FTUJFUl9JTUFHRVMiLCJSRUFEX09ORV9UUkFJTklOR19TRVNTSU9OX0RVRSIsIkNSRVdfVFJBSU5JTkdfVFlQRVMiLCJUUkFJTklOR19TRVNTSU9OX0JZX0lEIiwiVFJBSU5JTkdfVFlQRV9CWV9JRCIsInVzZU11dGF0aW9uIiwidXNlTGF6eVF1ZXJ5IiwidXNlUm91dGVyIiwiRWRpdG9yIiwiZ2V0UGVybWlzc2lvbnMiLCJoYXNQZXJtaXNzaW9uIiwiTG9hZGluZyIsIkxhYmVsIiwiQ29tYm9ib3giLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiU2VwYXJhdG9yIiwiU2hlZXQiLCJTaGVldENvbnRlbnQiLCJTaGVldEhlYWRlciIsIlNoZWV0VGl0bGUiLCJCdXR0b24iLCJTaWduYXR1cmVQYWQiLCJGb290ZXJXcmFwcGVyIiwiQ2hlY2tGaWVsZCIsIkNoZWNrRmllbGRDb250ZW50IiwiRGFpbHlDaGVja0ZpZWxkIiwiRGF0ZVBpY2tlciIsInVzZVRvYXN0IiwiQXJyb3dMZWZ0IiwiSDQiLCJBbGVydERpYWxvZyIsIkFsZXJ0RGlhbG9nQWN0aW9uIiwiQWxlcnREaWFsb2dDYW5jZWwiLCJBbGVydERpYWxvZ0NvbnRlbnQiLCJBbGVydERpYWxvZ0Rlc2NyaXB0aW9uIiwiQWxlcnREaWFsb2dGb290ZXIiLCJBbGVydERpYWxvZ0hlYWRlciIsIkFsZXJ0RGlhbG9nVGl0bGUiLCJUZXh0YXJlYSIsIlJlYWRWZXNzZWxzIiwiVHJhaW5pbmdGb3JtIiwidHJhaW5pbmdJRCIsIm1lbWJlcklkIiwidHJhaW5pbmdUeXBlSWQiLCJ2ZXNzZWxJZCIsInJhd1RyYWluaW5nIiwidmVzc2VscyIsInJvdXRlciIsInRvYXN0IiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwidHJhaW5pbmciLCJzZXRUcmFpbmluZyIsInNldFJhd1RyYWluaW5nIiwidHJhaW5pbmdEYXRlIiwic2V0VHJhaW5pbmdEYXRlIiwiRGF0ZSIsImhhc0Zvcm1FcnJvcnMiLCJzZXRIYXNGb3JtRXJyb3JzIiwic2VsZWN0ZWRNZW1iZXJMaXN0Iiwic2V0U2VsZWN0ZWRNZW1iZXJMaXN0Iiwic2lnbmF0dXJlTWVtYmVycyIsInNldFNpZ25hdHVyZU1lbWJlcnMiLCJzZXRWZXNzZWxzIiwidHJhaW5pbmdUeXBlcyIsInNldFRyYWluaW5nVHlwZXMiLCJjb250ZW50Iiwic2V0Q29udGVudCIsIm9wZW5WaWV3UHJvY2VkdXJlIiwic2V0T3BlblZpZXdQcm9jZWR1cmUiLCJjdXJyZW50Q29tbWVudCIsInNldEN1cnJlbnRDb21tZW50IiwiY3VycmVudEZpZWxkIiwic2V0Q3VycmVudEZpZWxkIiwiYnVmZmVyUHJvY2VkdXJlQ2hlY2siLCJzZXRCdWZmZXJQcm9jZWR1cmVDaGVjayIsImJ1ZmZlckZpZWxkQ29tbWVudCIsInNldEJ1ZmZlckZpZWxkQ29tbWVudCIsIm9wZW5Db21tZW50QWxlcnQiLCJzZXRPcGVuQ29tbWVudEFsZXJ0IiwiZm9ybUVycm9ycyIsInNldEZvcm1FcnJvcnMiLCJUcmFpbmluZ1R5cGVzIiwiVHJhaW5lcklEIiwiVmVzc2VsSUQiLCJ2ZXNzZWxJRCIsInNldFZlc3NlbElEIiwiZmllbGRJbWFnZXMiLCJzZXRGaWVsZEltYWdlcyIsInF1ZXJ5VHJhaW5pbmdUeXBlcyIsImZldGNoUG9saWN5Iiwib25Db21wbGV0ZWQiLCJyZXNwb25zZSIsImRhdGEiLCJyZWFkVHJhaW5pbmdUeXBlcyIsIm5vZGVzIiwib25FcnJvciIsImVycm9yIiwiY29uc29sZSIsInF1ZXJ5VHJhaW5pbmdTZXNzaW9uQnlJRCIsInJlYWRPbmVUcmFpbmluZ1Nlc3Npb24iLCJoYW5kbGVTZXRUcmFpbmluZyIsInF1ZXJ5VHJhaW5pbmdUeXBlQnlJRCIsInJlYWRPbmVUcmFpbmluZ1R5cGUiLCJwcmV2VHJhaW5pbmciLCJpZCIsImdldEZpZWxkSW1hZ2VzIiwicmVhZENhcHR1cmVJbWFnZXMiLCJ0RGF0ZSIsImRhdGUiLCJ0cmFpbmluZ0RhdGEiLCJJRCIsImZvcm1hdCIsIk1lbWJlcnMiLCJtZW1iZXJzIiwibWFwIiwibSIsInRyYWluZXIiLCJUcmFpbmluZ1N1bW1hcnkiLCJ0cmFpbmluZ1N1bW1hcnkiLCJ0IiwibGFiZWwiLCJmaXJzdE5hbWUiLCJzdXJuYW1lIiwidmFsdWUiLCJ2ZXNzZWxDcmV3SWRzIiwidmVzc2VsIiwic2VhTG9nc01lbWJlcnMiLCJzbG0iLCJ2ZXNzZWxDcmV3cyIsImZpbHRlciIsImluY2x1ZGVzIiwic2lnbmF0dXJlcyIsInMiLCJNZW1iZXJJRCIsIm1lbWJlciIsIlNpZ25hdHVyZURhdGEiLCJzaWduYXR1cmVEYXRhIiwicHJvY2VkdXJlRmllbGRzIiwiZXhpc3RpbmdQcm9jZWR1cmVDaGVja3MiLCJmaWVsZCIsImZpZWxkSWQiLCJjdXN0b21pc2VkQ29tcG9uZW50RmllbGRJRCIsInN0YXR1cyIsImV4aXN0aW5nRmllbGRDb21tZW50cyIsImNvbW1lbnQiLCJ2YXJpYWJsZXMiLCJ0cmFpbmluZ1Nlc3Npb25JRCIsImVxIiwicmVmcmVzaEltYWdlcyIsImhhbmRsZVNldFZlc3NlbHMiLCJhY3RpdmVWZXNzZWxzIiwiYXJjaGl2ZWQiLCJmb3JtYXR0ZWREYXRhIiwidGl0bGUiLCJxdWVyeVZlc3NlbHMiLCJxdWVyeVZlc3NlbFJlc3BvbnNlIiwicmVhZFZlc3NlbHMiLCJsb2FkVmVzc2VscyIsImxpbWl0Iiwib2Zmc2V0IiwibXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb24iLCJsb2FkaW5nIiwibXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb25Mb2FkaW5nIiwiY3JlYXRlVHJhaW5pbmdTZXNzaW9uIiwibGVuZ3RoIiwicHJvY2VkdXJlRmllbGQiLCJmaW5kIiwiZm9yRWFjaCIsImNyZWF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGEiLCJpbnB1dCIsInVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZXMiLCJ1cGRhdGVTaWduYXR1cmVzIiwiaGFuZGxlRWRpdG9yQ2hhbmdlIiwicHVzaCIsIm11dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uIiwibXV0YXRpb25VcGRhdGVUcmFpbmluZ1Nlc3Npb25Mb2FkaW5nIiwidXBkYXRlVHJhaW5pbmdTZXNzaW9uIiwiZXhpc3RpbmdGaWVsZCIsInVwZGF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGEiLCJjcmVhdGVJbnB1dCIsInJlYWRPbmVUcmFpbmluZ1Nlc3Npb25EdWUiLCJyZWFkT25lVHJhaW5pbmdTZXNzaW9uRHVlTG9hZGluZyIsImdldFRyYWluaW5nU2Vzc2lvbkR1ZVdpdGhWYXJpYWJsZXMiLCJtdXRhdGlvbkNyZWF0ZVRyYWluaW5nU2Vzc2lvbkR1ZSIsImNyZWF0ZVRyYWluaW5nU2Vzc2lvbkR1ZUxvYWRpbmciLCJtdXRhdGlvblVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZSIsInVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZUxvYWRpbmciLCJ0cmFpbmluZ1Nlc3Npb25EdWVzIiwidHJhaW5pbmdJbmZvIiwidHQiLCJvY2N1cnNFdmVyeSIsInRyYWluaW5nVHlwZUlEIiwibmV3RHVlRGF0ZSIsImFkZCIsIm1lbWJlcklEIiwiZHVlRGF0ZSIsInRyYWluaW5nU2Vzc2lvbkR1ZVdpdGhJRHMiLCJQcm9taXNlIiwiYWxsIiwiaXRlbSIsIkFycmF5IiwiZnJvbSIsImhhbmRsZVNhdmUiLCJoYXNFcnJvcnMiLCJlcnJvcnMiLCJUcmFpbmluZ0xvY2F0aW9uSUQiLCJpc1ZhbGlkIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50Iiwiam9pbiIsInRyYWluZXJJRCIsInRyYWluaW5nTG9jYXRpb25UeXBlIiwiVHJhaW5pbmdJRCIsInNpZ25hdHVyZSIsImNoZWNrQW5kU2F2ZVNpZ25hdHVyZSIsInF1ZXJ5R2V0TWVtYmVyVHJhaW5pbmdTaWduYXR1cmVzIiwiaW4iLCJ0aGVuIiwicmVhZE1lbWJlclRyYWluaW5nX1NpZ25hdHVyZXMiLCJtdXRhdGlvblVwZGF0ZU1lbWJlclRyYWluaW5nU2lnbmF0dXJlIiwibXV0YXRpb25DcmVhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZSIsImNhdGNoIiwibXV0YXRpb25VcGRhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZUxvYWRpbmciLCJ1cGRhdGVNZW1iZXJUcmFpbmluZ19TaWduYXR1cmUiLCJtdXRhdGlvbkNyZWF0ZU1lbWJlclRyYWluaW5nU2lnbmF0dXJlTG9hZGluZyIsImNyZWF0ZU1lbWJlclRyYWluaW5nX1NpZ25hdHVyZSIsImhhbmRsZVRyYWluaW5nRGF0ZUNoYW5nZSIsInRvU3RyaW5nIiwiaGFuZGxlVHJhaW5lckNoYW5nZSIsIm1lbWJlcnNTZXQiLCJTZXQiLCJwcmV2TGlzdCIsInByZXZTaWduYXR1cmVzIiwiaGFuZGxlVHJhaW5pbmdUeXBlQ2hhbmdlIiwiaGFuZGxlTWVtYmVyQ2hhbmdlIiwibG9nIiwibWVtYmVyc0xlbmd0aCIsIm1lbWJlcnNUeXBlIiwibWVtYmVySWRzIiwic29tZSIsIm9uU2lnbmF0dXJlQ2hhbmdlZCIsImUiLCJpbmRleCIsImZpbmRJbmRleCIsIm9iamVjdCIsInVwZGF0ZWRNZW1iZXJzIiwidHJpbSIsInNwbGljZSIsImhhbmRsZVRyYWluaW5nVmVzc2VsQ2hhbmdlIiwiaXNBcnJheSIsIm5ld0NvbnRlbnQiLCJ2aWQiLCJpc05hTiIsInBhcnNlSW50IiwicGVybWlzc2lvbnMiLCJzZXRQZXJtaXNzaW9ucyIsImVycm9yTWVzc2FnZSIsImdldFByb2NlZHVyZXMiLCJwcm9jZWR1cmVzIiwidHlwZSIsImN1c3RvbWlzZWRDb21wb25lbnRGaWVsZCIsImZpZWxkcyIsInNvcnQiLCJhIiwiYiIsInNvcnRPcmRlciIsImNyZXdWYWx1ZSIsInVzZU1lbW8iLCJoYXNUcmFpbmluZ1Byb2NlZHVyZXMiLCJwcm9jZWR1cmUiLCJoYW5kbGVQcm9jZWR1cmVDaGVja3MiLCJwcm9jZWR1cmVDaGVjayIsImdldEZpZWxkU3RhdHVzIiwiZmllbGRTdGF0dXMiLCJzaG93Q29tbWVudFBvcHVwIiwiZmllbGRDb21tZW50IiwiZ2V0Q29tbWVudCIsImhhbmRsZVNhdmVDb21tZW50IiwiaGFuZGxlVmlld1Byb2NlZHVyZXMiLCJoYW5kbGVDYW5jZWxDbGljayIsImhhbmRsZUNvbW1lbnRDaGFuZ2UiLCJ0YXJnZXQiLCJjbGFzc05hbWUiLCJkaXYiLCJwIiwib25DbGljayIsIm9uQ2hhbmdlIiwic21hbGwiLCJtb2RlIiwib3B0aW9ucyIsImRlZmF1bHRWYWx1ZXMiLCJ0cmFpbmluZ0xvY2F0aW9uIiwicGxhY2Vob2xkZXIiLCJkaXNwbGF5RmllbGQiLCJkaXNwbGF5RGVzY3JpcHRpb24iLCJkaXNwbGF5TGFiZWwiLCJmaWVsZE5hbWUiLCJpbnB1dElkIiwiaGFuZGxlTm9DaGFuZ2UiLCJkZWZhdWx0Tm9DaGVja2VkIiwiaGFuZGxlWWVzQ2hhbmdlIiwiZGVmYXVsdFllc0NoZWNrZWQiLCJjb21tZW50QWN0aW9uIiwiZGlzcGxheUltYWdlIiwib25JbWFnZVVwbG9hZCIsInNlY3Rpb25EYXRhIiwic2VjdGlvbk5hbWUiLCJzaWciLCJpY29uTGVmdCIsImRpc2FibGVkIiwib3BlbiIsIm9uT3BlbkNoYW5nZSIsInNpZGUiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsInJvd3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx\n"));

/***/ })

});