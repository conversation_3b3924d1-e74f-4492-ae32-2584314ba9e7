"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx":
/*!**************************************************!*\
  !*** ./src/app/ui/crew-training/create/form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew-training/create/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TrainingForm = (param)=>{\n    let { trainingID = 0, memberId = 0, trainingTypeId = 0, vesselId = 0 } = param;\n    var _rawTraining_VesselID, _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _vessels_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Date());\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [vesselID, setVesselID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(vesselId);\n    const [fieldImages, setFieldImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // GraphQL queries to replace the problematic function calls\n    const [queryTrainingTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.CREW_TRAINING_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingTypes.nodes;\n            if (data) {\n                setTrainingTypes(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingTypes error\", error);\n        }\n    });\n    const [queryTrainingSessionByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.TRAINING_SESSION_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingSession;\n            if (data) {\n                handleSetTraining(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingSession error\", error);\n        }\n    });\n    const [queryTrainingTypeByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.TRAINING_TYPE_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingType;\n            if (data) {\n                setTraining((prevTraining)=>({\n                        ...prevTraining,\n                        TrainingTypes: [\n                            data.id\n                        ]\n                    }));\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingType error\", error);\n        }\n    });\n    const [getFieldImages] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_SECTION_MEMBER_IMAGES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCaptureImages.nodes;\n            if (data) {\n                setFieldImages(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getFieldImages error\", error);\n        }\n    });\n    // Define handleSetTraining function\n    const handleSetTraining = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((training)=>{\n        var _training_procedureFields;\n        const tDate = new Date(training.date);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.date).format(\"YYYY-MM-DD\"),\n            Members: training.members.nodes.map((m)=>m.id),\n            TrainerID: training.trainer.id,\n            TrainingSummary: training.trainingSummary,\n            TrainingTypes: training.trainingTypes.nodes.map((t)=>t.id),\n            // VesselID: training.vessel.id,\n            VesselID: training.vesselID\n        };\n        const members = training.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        const vesselCrewIds = training.vessel.seaLogsMembers.nodes.map((slm)=>+slm.id);\n        const vesselCrews = members.filter((m)=>vesselCrewIds.includes(+m.value));\n        const signatures = training.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        // Batch all state updates using React's automatic batching\n        setTrainingDate(tDate);\n        setRawTraining(training);\n        setTraining(trainingData);\n        setContent(training.trainingSummary);\n        setSelectedMemberList(vesselCrews);\n        setSignatureMembers(signatures);\n        // Initialize buffer with existing procedure field data for updates\n        if ((_training_procedureFields = training.procedureFields) === null || _training_procedureFields === void 0 ? void 0 : _training_procedureFields.nodes) {\n            const existingProcedureChecks = training.procedureFields.nodes.map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    status: field.status === \"Ok\"\n                }));\n            const existingFieldComments = training.procedureFields.nodes.filter((field)=>field.comment).map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    comment: field.comment\n                }));\n            setBufferProcedureCheck(existingProcedureChecks);\n            setBufferFieldComment(existingFieldComments);\n        }\n    }, [\n        trainingID\n    ]);\n    // useEffect hooks to replace the problematic function calls\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training types on component mount\n        queryTrainingTypes();\n    }, []) // Empty dependency array - only run once on mount\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training session by ID when trainingID changes\n        if (trainingID > 0) {\n            queryTrainingSessionByID({\n                variables: {\n                    id: trainingID\n                }\n            });\n        }\n    }, [\n        trainingID\n    ]) // Only depend on trainingID\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Load training type by ID when trainingTypeId changes\n        if (trainingTypeId > 0) {\n            queryTrainingTypeByID({\n                variables: {\n                    id: trainingTypeId\n                }\n            });\n        }\n    }, [\n        trainingTypeId\n    ]) // Only depend on trainingTypeId\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (trainingID > 0) {\n            getFieldImages({\n                variables: {\n                    filter: {\n                        trainingSessionID: {\n                            eq: trainingID\n                        }\n                    }\n                }\n            });\n        }\n    }, [\n        trainingID\n    ]) // Only depend on trainingID\n    ;\n    const refreshImages = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async ()=>{\n        await getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    }, [\n        getFieldImages,\n        trainingID\n    ]);\n    const handleSetVessels = (data)=>{\n        const activeVessels = data === null || data === void 0 ? void 0 : data.filter((vessel)=>!vessel.archived);\n        const formattedData = [\n            {\n                label: \"Other\",\n                value: \"Other\"\n            },\n            {\n                label: \"Desktop/shore\",\n                value: \"Onshore\"\n            },\n            ...activeVessels.map((vessel)=>({\n                    value: vessel.id,\n                    label: vessel.title\n                }))\n        ];\n        setVessels(formattedData);\n    };\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_28__.ReadVessels, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isLoading) {\n            const loadVessels = async ()=>{\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            };\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        queryVessels\n    ]);\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                router.push(\"/crew-training\");\n            } else {\n                console.error(\"mutationCreateUser error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                // Handle procedure checks for updates\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields, _bufferFieldComment_find;\n                        const existingField = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((field)=>field.customisedComponentFieldID === procedureField.fieldId);\n                        return {\n                            id: existingField === null || existingField === void 0 ? void 0 : existingField.id,\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        if (procedureField.id) {\n                            // Update existing field\n                            updateCustomisedComponentFieldData({\n                                variables: {\n                                    input: procedureField\n                                }\n                            });\n                        } else {\n                            // Create new field\n                            const { id, ...createInput } = procedureField;\n                            createCustomisedComponentFieldData({\n                                variables: {\n                                    input: createInput\n                                }\n                            });\n                        }\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n                if (+memberId > 0) {\n                    router.push(\"/crew/info?id=\".concat(memberId));\n                } else if (+vesselId > 0) {\n                    router.push(\"/vessel/info?id=\".concat(vesselId));\n                } else {\n                    router.push(\"/crew-training\");\n                }\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        const { data } = await readOneTrainingSessionDue({\n            variables: variables\n        });\n        onCompleted(data.readOneTrainingSessionDue);\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    await mutationCreateTrainingSessionDue(variables);\n                } else {\n                    await mutationUpdateTrainingSessionDue(variables);\n                }\n            }));\n        }\n    };\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            toast({\n                title: \"Error\",\n                description: errors.TrainingTypes || errors.TrainerID || errors.VesselID || errors.Date,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\"\n        };\n        if (trainingID === 0) {\n            await mutationCreateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        } else {\n            await mutationUpdateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        }\n    }, [\n        training,\n        trainingID,\n        content,\n        bufferProcedureCheck,\n        bufferFieldComment,\n        rawTraining,\n        memberId,\n        vesselId,\n        mutationCreateTrainingSession,\n        mutationUpdateTrainingSession,\n        router,\n        toast\n    ]);\n    // var signatureCount = 0\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        await queryGetMemberTrainingSignatures({\n            variables: {\n                filter: {\n                    memberID: {\n                        eq: signature.MemberID\n                    },\n                    trainingSessionID: {\n                        in: TrainingID\n                    }\n                }\n            }\n        }).then((response)=>{\n            const data = response.data.readMemberTraining_Signatures.nodes;\n            if (data.length > 0) {\n                mutationUpdateMemberTrainingSignature({\n                    variables: {\n                        input: {\n                            id: data[0].id,\n                            memberID: signature.MemberID,\n                            signatureData: signature.SignatureData,\n                            trainingSessionID: TrainingID\n                        }\n                    }\n                });\n            } else {\n                if (signature.SignatureData) {\n                    mutationCreateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"mutationGetMemberTrainingSignatures error\", error);\n        });\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((date)=>{\n        setTrainingDate(date && new Date(date.toString()));\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n            }));\n    }, []);\n    const handleTrainerChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((trainer)=>{\n        if (!trainer) return; // Add early return if trainer is null\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        setTraining((prevTraining)=>{\n            const membersSet = new Set((prevTraining === null || prevTraining === void 0 ? void 0 : prevTraining.Members) || []);\n            membersSet.add(trainer.value);\n            const members = Array.from(membersSet);\n            return {\n                ...prevTraining,\n                TrainerID: trainer.value,\n                Members: members\n            };\n        });\n        setSelectedMemberList((prevList)=>[\n                ...prevList,\n                trainer\n            ]);\n        setSignatureMembers((prevSignatures)=>[\n                ...prevSignatures,\n                {\n                    MemberID: +trainer.value,\n                    SignatureData: null\n                }\n            ]);\n    }, []);\n    const handleTrainingTypeChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((trainingTypes)=>{\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                TrainingTypes: trainingTypes.map((item)=>item.value)\n            }));\n    }, []);\n    /* const handleTrainingLocationChange = (vessel: any) => {\r\n        setTraining({\r\n            ...training,\r\n            VesselID: vessel.isVessel ? vessel.value : 0,\r\n            TrainingLocationID: !vessel.isVessel ? vessel.value : 0,\r\n        })\r\n    } */ const handleMemberChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((members)=>{\n        console.log(\"\\uD83D\\uDD27 TrainingForm - handleMemberChange called with:\", {\n            members,\n            membersLength: members === null || members === void 0 ? void 0 : members.length,\n            membersType: typeof members\n        });\n        const memberIds = members.map((item)=>item.value);\n        console.log(\"\\uD83D\\uDD27 TrainingForm - extracted member IDs:\", memberIds);\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                Members: memberIds\n            }));\n        setSelectedMemberList(members);\n        setSignatureMembers((prevSignatures)=>prevSignatures.filter((item)=>members.some((m)=>+m.value === item.MemberID)));\n    }, []);\n    const onSignatureChanged = (e, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (e) {\n            if (index !== -1) {\n                if (e.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = e;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: e\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    const handleTrainingVesselChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((vessel)=>{\n        setTraining((prevTraining)=>({\n                ...prevTraining,\n                VesselID: vessel ? typeof vessel === \"object\" && !Array.isArray(vessel) ? vessel.value : 0 : 0\n            }));\n    }, []);\n    const handleEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((newContent)=>{\n        setContent(newContent);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training)) {\n            const vid = vesselId > 0 || isNaN(parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10)) ? vesselId : parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10);\n            setVesselID(vid);\n        }\n    }, [\n        vesselId,\n        training\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.getPermissions);\n    }, []);\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 936,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 938,\n            columnNumber: 13\n        }, undefined);\n    }\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            var _this;\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: (_this = [\n                    ...type.customisedComponentField.nodes\n                ]) === null || _this === void 0 ? void 0 : _this.sort((a, b)=>a.sortOrder - b.sortOrder)\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const vesselOptions = useMemo(()=>{\n        if (!vessels) return [];\n        return vessels.map((vessel)=>({\n                label: vessel.label,\n                value: vessel.value\n            }));\n    }, [\n        vessels\n    ]);\n    const crewValue = useMemo(()=>{\n        return memberId > 0 ? [\n            memberId.toString()\n        ] : training === null || training === void 0 ? void 0 : training.Members;\n    }, [\n        memberId,\n        training === null || training === void 0 ? void 0 : training.Members\n    ]);\n    const hasTrainingProcedures = useMemo(()=>{\n        return training && trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n        }).length > 0;\n    }, [\n        training,\n        trainingTypes\n    ]);\n    const handleProcedureChecks = (field, type, status)=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n        setBufferProcedureCheck([\n            ...procedureCheck,\n            {\n                fieldId: field.id,\n                status: status\n            }\n        ]);\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n        setBufferFieldComment([\n            ...fieldComment,\n            {\n                fieldId: currentField.id,\n                comment: currentComment\n            }\n        ]);\n        setOpenCommentAlert(false);\n    }, [\n        bufferFieldComment,\n        currentField,\n        currentComment\n    ]);\n    const handleViewProcedures = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        setOpenViewProcedure(true);\n    }, []);\n    const handleCancelClick = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        router.push(\"/crew-training\");\n    }, [\n        router\n    ]);\n    const handleCommentChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((e)=>{\n        setCurrentComment(e.target.value);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                className: \"mb-2.5 mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardTitle, {\n                            children: [\n                                trainingID === 0 ? \"New\" : \"Edit\",\n                                \" Training Session\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1074,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1073,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                        className: \"my-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1078,\n                        columnNumber: 17\n                    }, undefined),\n                    !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1080,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                        className: \"p-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 my-4 \",\n                                        children: [\n                                            \"Training Details\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \" mt-4 max-w-[25rem] leading-loose mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1086,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            hasTrainingProcedures && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                                onClick: handleViewProcedures,\n                                                children: \"View Procedures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1088,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1084,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-2 pt-8 pb-5 space-y-6 px-7 border border-border border-dashed rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full my-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                label: \"Trainer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                                                    vesselID: vesselID,\n                                                                    onChange: handleTrainerChange\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                    lineNumber: 1097,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1096,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.TrainerID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1103,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1095,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full md:mt-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                                                onChange: handleTrainingTypeChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1109,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-red-vivid-500\",\n                                                                children: hasFormErrors && formErrors.TrainingTypes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1113,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1108,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full mt-4 flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                        children: \"Crew\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        value: crewValue,\n                                                        vesselID: vesselID,\n                                                        onChange: handleMemberChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1121,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1119,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                                                className: \"my-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1127,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex w-full gap-4 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full \",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                mode: \"single\",\n                                                                onChange: handleTrainingDateChange,\n                                                                value: new Date(trainingDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1134,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.Date\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1139,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            vessels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                                                options: vesselOptions,\n                                                                defaultValues: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_VesselID = rawTraining.VesselID) === null || _rawTraining_VesselID === void 0 ? void 0 : _rawTraining_VesselID.toString() : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? \"Desktop/shore\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? \"Other\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.id.toString() : vesselId ? {\n                                                                    label: (_vessels_find = vessels.find((vessel)=>vessel.value === vesselId)) === null || _vessels_find === void 0 ? void 0 : _vessels_find.label,\n                                                                    value: vesselId.toString()\n                                                                } : null,\n                                                                isLoading: rawTraining,\n                                                                onChange: handleTrainingVesselChange,\n                                                                placeholder: \"Select location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1145,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.VesselID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1184,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1143,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1128,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3 md:col-span-2\",\n                                                children: [\n                                                    getProcedures().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: getProcedures().map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.CheckField, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        label: type.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1205,\n                                                                        columnNumber: 57\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.CheckFieldContent, {\n                                                                        children: type.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.DailyCheckField, {\n                                                                                displayField: field.status === \"Required\",\n                                                                                displayDescription: field.description,\n                                                                                displayLabel: field.fieldName,\n                                                                                inputId: field.id,\n                                                                                handleNoChange: ()=>handleProcedureChecks(field, type, false),\n                                                                                defaultNoChecked: getFieldStatus(field) === \"Not_Ok\",\n                                                                                handleYesChange: ()=>handleProcedureChecks(field, type, true),\n                                                                                defaultYesChecked: getFieldStatus(field) === \"Ok\",\n                                                                                commentAction: ()=>showCommentPopup(field),\n                                                                                comment: getComment(field),\n                                                                                displayImage: trainingID > 0,\n                                                                                fieldImages: fieldImages,\n                                                                                onImageUpload: refreshImages,\n                                                                                sectionData: {\n                                                                                    id: trainingID,\n                                                                                    sectionName: \"trainingSessionID\"\n                                                                                }\n                                                                            }, field.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                                lineNumber: 1213,\n                                                                                columnNumber: 69\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1208,\n                                                                        columnNumber: 57\n                                                                    }, undefined)\n                                                                ]\n                                                            }, type.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1204,\n                                                                columnNumber: 53\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1201,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"my-4 flex items-center w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            id: \"TrainingSummary\",\n                                                            placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                                                            className: \"!w-full  ring-1 ring-inset \",\n                                                            handleEditorChange: handleEditorChange,\n                                                            content: content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1289,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1288,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1199,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1093,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1083,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_17__.Separator, {\n                                className: \"my-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1310,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 md:my-4 \",\n                                        children: \"Signatures\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1312,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 sm:col-span-2 md:my-4 flex justify-between flex-wrap gap-4\",\n                                        children: selectedMemberList && selectedMemberList.map((member, index)=>{\n                                            var _signatureMembers_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full md:w-96\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-full\",\n                                                    member: member.label,\n                                                    memberId: member.value,\n                                                    onSignatureChanged: (signature, member, memberId)=>onSignatureChanged(signature, member !== null && member !== void 0 ? member : \"\", memberId || 0),\n                                                    signature: {\n                                                        id: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.ID\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1322,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1319,\n                                                columnNumber: 45\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1315,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1311,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1082,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1072,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_21__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        variant: \"back\",\n                        onClick: handleCancelClick,\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1354,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        onClick: handleSave,\n                        disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                        children: trainingID === 0 ? \"Create session\" : \"Update session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1366,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1353,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[400px] sm:w-[540px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1378,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1377,\n                            columnNumber: 21\n                        }, undefined),\n                        training && trainingTypes.filter((type)=>{\n                            var _training_TrainingTypes;\n                            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                        }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 px-2.5 sm:px-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_25__.H4, {\n                                        children: type.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: type.procedure\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1395,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, type.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1390,\n                                columnNumber: 33\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1376,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1375,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialog, {\n                open: openCommentAlert,\n                onOpenChange: setOpenCommentAlert,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogTitle, {\n                                    children: \"Add Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1409,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogDescription, {\n                                    children: \"Add a comment for this procedure check.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1410,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1408,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_27__.Textarea, {\n                            value: currentComment,\n                            onChange: handleCommentChange,\n                            placeholder: \"Enter your comment here...\",\n                            rows: 4\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1414,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1421,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogAction, {\n                                    onClick: handleSaveComment,\n                                    children: \"Save Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1422,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1420,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1407,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1404,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TrainingForm, \"MkkCah8iL8yNvg6XyhQMF0HbdNY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation\n    ];\n});\n_c = TrainingForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingForm);\nvar _c;\n$RefreshReg$(_c, \"TrainingForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx\n"));

/***/ })

});